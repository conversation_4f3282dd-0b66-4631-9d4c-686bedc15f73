rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Admin users can access all files
    match /{allPaths=**} {
      allow read, write: if hasAdminAccess();
    }

    // Question images - admin access for management
    match /questions/{questionId}/{imageFile} {
      allow read, write: if hasAdminAccess();
    }

    // Helper function to check admin access
    function hasAdminAccess() {
      return request.auth != null &&
             request.auth.token.role != null &&
             request.auth.token.role in ['super_admin', 'manager', 'content_creator'];
    }
  }
}
