name: medpulse_repo
publish_to: none

environment:
  sdk: ">=3.6.0 <4.0.0"
  flutter: ">=3.27.3"

dependencies:
  flutter:
    sdk: flutter
  collection: ^1.19.0
  excel: ^4.0.6

dev_dependencies:
  build_runner: ^2.4.14
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  melos:  ^7.0.0-dev.5
  flutter_flavorizr: ^2.2.3
  cider: ^0.2.8

flutter:
  uses-material-design: true

melos:
  name: medpulse

  scripts:
    analyze:all:
      description: Run analyze for all packages
      run: melos exec -- "flutter analyze --no-fatal-warnings"

    runner:watch:
      description: Run buildrunner watch on all packages at the same time
      run: ./br.ps1

    test:all:
      description: Run tests for all packages
      run: melos exec -- "flutter test"

    # Firebase Emulator
    emulator:
      description: Run emulators and load/save data locally
      run: firebase emulators:start  --import=./.emulator_data --export-on-exit ./.emulator_data

    emulator:export:
      description: Export emulator data
      run: firebase emulators:export ./.emulator_data

    # Web admin
    admin:dev:
      description: Run admin web app
      run: cd apps/admin && flutter run -d chrome

    # Build commands
    build:admin:dev:
      description: Build admin dev app
      run: cd apps/admin && flutter build web --debug

    build:admin:prod:
      description: Build admin prod app
      run: cd apps/admin && flutter build web --release

    build:medpulse:dev:
      description: Build medpulse dev app
      run: cd apps/medpulse && flutter build web --debug

    build:medpulse:prod:
      description: Build medpulse prod app
      run: cd apps/medpulse && flutter build web --release

    # Version management
#    version:bump:
#      description: Bump version across packages
#      run: |
#        melos version --no-git-tag-version
#        cd apps/medpulse && cider bump build

    # Deployment
    deploy:functions:
      description: Deploy Firebase functions
      run: cd functions && firebase deploy --only functions

    deploy:admin:
      description: Deploy admin web app
      run: cd apps/admin && ./copy_assets.sh && fvm flutter build web --release && firebase deploy --only hosting

  sdkPath: .fvm/flutter_sdk

  environment:
    sdk: ">=3.0.0 <4.0.0"
    flutter: ">=3.10.0"

  command:
    bootstrap:
      concurrent: false
    run:
      concurrent: false
  #   usePubspecOverrides: true
