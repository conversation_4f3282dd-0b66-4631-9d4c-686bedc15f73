# MedPulse Admin Creation Script

Simple script to create admin users for the MedPulse admin panel.

## 🔧 Setup Required

**Service Account Keys Required:**

For **Development** environment:
1. Go to [Firebase Console > medpulsedev > Service Accounts](https://console.firebase.google.com/project/medpulsedev/settings/serviceaccounts/adminsdk)
2. Click "Generate new private key"
3. Download the JSON file
4. Save it as `medpulsedev-service-account-key.json` in this directory

For **Production** environment:
1. Go to [Firebase Console > medpulse-prod > Service Accounts](https://console.firebase.google.com/project/medpulse-prod/settings/serviceaccounts/adminsdk)
2. Click "Generate new private key"
3. Download the JSON file
4. Save it as `medpulse-prod-service-account-key.json` in this directory

## 📋 Create Admin User

```bash
# Create admin for development
node create-admin.js --email <EMAIL> --password MyPassword123 --name "Admin User" --env dev

# Create admin for production
node create-admin.js --email <EMAIL> --password SecurePass123 --name "Admin" --env prod
```

**Required parameters:**
- `--email` - Admin email address
- `--password` - Admin password (min 6 characters)
- `--env` - Environment: `dev` or `prod`

**Optional parameters:**
- `--name` - Display name for the admin

## 📁 Files Structure

```
scripts/
├── create-admin.js                        # Admin creation script
├── medpulsedev-service-account-key.json   # ← YOU NEED TO ADD THIS (dev)
├── medpulse-prod-service-account-key.json # ← YOU NEED TO ADD THIS (prod)
├── package.json                           # NPM scripts configuration
└── README.md                              # This documentation
```

## ⚠️ Security Notes

- **Strong Passwords**: Use secure passwords for admin accounts
- **Service Account Keys**: Keep service account keys secure and never commit them to version control
- **Environment Separation**: Use different credentials for dev and prod environments
