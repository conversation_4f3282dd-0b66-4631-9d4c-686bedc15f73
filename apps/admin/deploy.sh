#!/bin/bash

# MedPulse Admin Panel Deployment Script
# Professional deployment automation for Firebase Hosting

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 MedPulse Admin Panel Deployment${NC}"
echo -e "${YELLOW}======================================${NC}"
echo ""

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo -e "${RED}❌ Firebase CLI is not installed.${NC}"
    echo -e "${BLUE}Install with: npm install -g firebase-tools${NC}"
    exit 1
fi

# Check if logged in to Firebase
if ! firebase projects:list &> /dev/null; then
    echo -e "${RED}❌ Not logged in to Firebase CLI.${NC}"
    echo -e "${BLUE}Login with: firebase login${NC}"
    exit 1
fi

# Check if FVM is available
if ! command -v fvm &> /dev/null; then
    echo -e "${RED}❌ FVM is not installed.${NC}"
    echo -e "${BLUE}Install FVM first: https://fvm.app/docs/getting_started/installation${NC}"
    exit 1
fi

echo -e "${BLUE}📋 Pre-deployment checks passed!${NC}"
echo ""

# Step 1: Copy assets
echo -e "${BLUE}📁 Step 1: Copying assets...${NC}"
./copy_assets.sh

# Step 2: Build Flutter web app
echo -e "${BLUE}🔨 Step 2: Building Flutter web app...${NC}"
fvm flutter build web --release

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Flutter build failed!${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Build completed successfully!${NC}"
echo ""

# Step 3: Deploy to Firebase Hosting
echo -e "${BLUE}🚀 Step 3: Deploying to Firebase Hosting...${NC}"
firebase deploy --only hosting:medpulse-admin

if [ $? -eq 0 ]; then
    echo ""
    echo -e "${GREEN}🎉 Deployment completed successfully!${NC}"
    echo ""
    echo -e "${BLUE}📊 Deployment Summary:${NC}"
    echo -e "   • Project: medpulse-prod"
    echo -e "   • Hosting Site: medpulse-admin"
    echo -e "   • URL: https://medpulse-admin.web.app"
    echo ""
    echo -e "${BLUE}🔗 Quick Links:${NC}"
    echo -e "   • Admin Panel: https://medpulse-admin.web.app"
    echo -e "   • Firebase Console: https://console.firebase.google.com/project/medpulse-prod/overview"
    echo ""
    echo -e "${YELLOW}🔐 Login with your super admin credentials:${NC}"
    echo -e "   • Email: <EMAIL>"
    echo -e "   • Role: Super Admin"
    echo ""
else
    echo -e "${RED}❌ Deployment failed!${NC}"
    exit 1
fi
