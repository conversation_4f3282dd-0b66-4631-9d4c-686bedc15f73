// apps/admin/lib/shared/widgets/loading_cards.dart

import 'package:flutter/material.dart';

/// A shimmer loading animation for statistics cards
class LoadingStatsCards extends StatefulWidget {
  const LoadingStatsCards({super.key});

  @override
  State<LoadingStatsCards> createState() => _LoadingStatsCardsState();
}

class _LoadingStatsCardsState extends State<LoadingStatsCards>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(
      begin: -1.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: List.generate(4, (index) => Expanded(
        child: Padding(
          padding: EdgeInsets.only(
            left: index == 0 ? 0 : 8,
            right: index == 3 ? 0 : 8,
          ),
          child: _buildLoadingCard(),
        ),
      )),
    );
  }

  Widget _buildLoadingCard() {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          child: Container(
            padding: const EdgeInsets.all(8),
            height: 76,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                stops: [
                  (_animation.value - 1).clamp(0.0, 1.0),
                  _animation.value.clamp(0.0, 1.0),
                  (_animation.value + 1).clamp(0.0, 1.0),
                ],
                colors: [
                  Colors.grey[300]!,
                  Colors.grey[100]!,
                  Colors.grey[300]!,
                ],
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Icon placeholder
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(height: 4),
                // Title placeholder
                Container(
                  width: 40,
                  height: 14,
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(3),
                  ),
                ),
                const SizedBox(height: 3),
                // Subtitle placeholder
                Container(
                  width: 55,
                  height: 8,
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(3),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

/// A simple pulsing loading animation for statistics cards
class PulsingStatsCards extends StatefulWidget {
  const PulsingStatsCards({super.key});

  @override
  State<PulsingStatsCards> createState() => _PulsingStatsCardsState();
}

class _PulsingStatsCardsState extends State<PulsingStatsCards>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _animation = Tween<double>(
      begin: 0.3,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: List.generate(4, (index) => Expanded(
        child: Padding(
          padding: EdgeInsets.only(
            left: index == 0 ? 0 : 8,
            right: index == 3 ? 0 : 8,
          ),
          child: _buildPulsingCard(),
        ),
      )),
    );
  }

  Widget _buildPulsingCard() {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Opacity(
          opacity: _animation.value,
          child: Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            child: Container(
              padding: const EdgeInsets.all(8),
              height: 76,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Icon placeholder
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Icon(
                      Icons.analytics_outlined,
                      color: Colors.grey[400],
                      size: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  // Title placeholder
                  Container(
                    width: 40,
                    height: 14,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(3),
                    ),
                  ),
                  const SizedBox(height: 3),
                  // Subtitle placeholder
                  Container(
                    width: 55,
                    height: 8,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(3),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

/// A wavy loading animation for question statistics cards
class WavyStatsCards extends StatefulWidget {
  const WavyStatsCards({super.key});

  @override
  State<WavyStatsCards> createState() => _WavyStatsCardsState();
}

/// Loading cards specifically for Users section
class UserLoadingCards extends StatefulWidget {
  const UserLoadingCards({super.key});

  @override
  State<UserLoadingCards> createState() => _UserLoadingCardsState();
}

/// Loading cards specifically for Courses section
class CourseLoadingCards extends StatefulWidget {
  const CourseLoadingCards({super.key});

  @override
  State<CourseLoadingCards> createState() => _CourseLoadingCardsState();
}

/// Loading cards specifically for Tests section
class TestLoadingCards extends StatefulWidget {
  const TestLoadingCards({super.key});

  @override
  State<TestLoadingCards> createState() => _TestLoadingCardsState();
}

class _WavyStatsCardsState extends State<WavyStatsCards>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _controllers = List.generate(4, (index) => AnimationController(
      duration: Duration(milliseconds: 800 + (index * 200)),
      vsync: this,
    ));

    _animations = _controllers.map((controller) => Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: controller,
      curve: Curves.easeInOut,
    ))).toList();

    // Start animations with staggered delays
    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 150), () {
        if (mounted) {
          _controllers[i].repeat(reverse: true);
        }
      });
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: List.generate(4, (index) => Expanded(
        child: Padding(
          padding: EdgeInsets.only(
            left: index == 0 ? 0 : 8,
            right: index == 3 ? 0 : 8,
          ),
          child: _buildWavyCard(index),
        ),
      )),
    );
  }

  Widget _buildWavyCard(int index) {
    return AnimatedBuilder(
      animation: _animations[index],
      builder: (context, child) {
        return Transform.scale(
          scale: 0.95 + (0.05 * _animations[index].value),
          child: Card(
            elevation: 2 + (2 * _animations[index].value),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            child: Container(
              padding: const EdgeInsets.all(8),
              height: 76,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: Colors.purple[50]?.withValues(alpha: 0.5 + (0.5 * _animations[index].value)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Question icon placeholder with rotation
                  Transform.rotate(
                    angle: _animations[index].value * 0.1,
                    child: Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: Colors.purple[300]?.withValues(alpha: 0.7 + (0.3 * _animations[index].value)),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Icon(
                        Icons.quiz,
                        color: Colors.purple[600],
                        size: 16,
                      ),
                    ),
                  ),
                  const SizedBox(height: 4),
                  // Title placeholder
                  Container(
                    width: 40,
                    height: 14,
                    decoration: BoxDecoration(
                      color: Colors.purple[300]?.withValues(alpha: 0.7 + (0.3 * _animations[index].value)),
                      borderRadius: BorderRadius.circular(3),
                    ),
                  ),
                  const SizedBox(height: 3),
                  // Subtitle placeholder
                  Container(
                    width: 55,
                    height: 8,
                    decoration: BoxDecoration(
                      color: Colors.purple[300]?.withValues(alpha: 0.7 + (0.3 * _animations[index].value)),
                      borderRadius: BorderRadius.circular(3),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

// User Loading Cards Implementation
class _UserLoadingCardsState extends State<UserLoadingCards>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(
      begin: -1.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: List.generate(4, (index) => Expanded(
        child: Padding(
          padding: EdgeInsets.only(
            left: index == 0 ? 0 : 8,
            right: index == 3 ? 0 : 8,
          ),
          child: _buildUserLoadingCard(),
        ),
      )),
    );
  }

  Widget _buildUserLoadingCard() {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          child: Container(
            padding: const EdgeInsets.all(8),
            height: 76,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                stops: [
                  (_animation.value - 1).clamp(0.0, 1.0),
                  _animation.value.clamp(0.0, 1.0),
                  (_animation.value + 1).clamp(0.0, 1.0),
                ],
                colors: [
                  Colors.blue[100]!,
                  Colors.blue[50]!,
                  Colors.blue[100]!,
                ],
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // User icon placeholder
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: Colors.blue[200],
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Icon(
                    Icons.people,
                    color: Colors.blue[400],
                    size: 16,
                  ),
                ),
                const SizedBox(height: 4),
                // Title placeholder
                Container(
                  width: 40,
                  height: 14,
                  decoration: BoxDecoration(
                    color: Colors.blue[200],
                    borderRadius: BorderRadius.circular(3),
                  ),
                ),
                const SizedBox(height: 3),
                // Subtitle placeholder
                Container(
                  width: 55,
                  height: 8,
                  decoration: BoxDecoration(
                    color: Colors.blue[200],
                    borderRadius: BorderRadius.circular(3),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

// Course Loading Cards Implementation
class _CourseLoadingCardsState extends State<CourseLoadingCards>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _animation = Tween<double>(
      begin: 0.3,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: List.generate(4, (index) => Expanded(
        child: Padding(
          padding: EdgeInsets.only(
            left: index == 0 ? 0 : 8,
            right: index == 3 ? 0 : 8,
          ),
          child: _buildCourseLoadingCard(),
        ),
      )),
    );
  }

  Widget _buildCourseLoadingCard() {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Opacity(
          opacity: _animation.value,
          child: Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            child: Container(
              padding: const EdgeInsets.all(8),
              height: 76,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Course icon placeholder
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: Colors.green[300],
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Icon(
                      Icons.school,
                      color: Colors.green[600],
                      size: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  // Title placeholder
                  Container(
                    width: 40,
                    height: 14,
                    decoration: BoxDecoration(
                      color: Colors.green[300],
                      borderRadius: BorderRadius.circular(3),
                    ),
                  ),
                  const SizedBox(height: 3),
                  // Subtitle placeholder
                  Container(
                    width: 55,
                    height: 8,
                    decoration: BoxDecoration(
                      color: Colors.green[300],
                      borderRadius: BorderRadius.circular(3),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

// Test Loading Cards Implementation
class _TestLoadingCardsState extends State<TestLoadingCards>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _controllers = List.generate(4, (index) => AnimationController(
      duration: Duration(milliseconds: 800 + (index * 200)),
      vsync: this,
    ));

    _animations = _controllers.map((controller) => Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: controller,
      curve: Curves.easeInOut,
    ))).toList();

    // Start animations with staggered delays
    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 150), () {
        if (mounted) {
          _controllers[i].repeat(reverse: true);
        }
      });
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: List.generate(4, (index) => Expanded(
        child: Padding(
          padding: EdgeInsets.only(
            left: index == 0 ? 0 : 8,
            right: index == 3 ? 0 : 8,
          ),
          child: _buildTestLoadingCard(index),
        ),
      )),
    );
  }

  Widget _buildTestLoadingCard(int index) {
    return AnimatedBuilder(
      animation: _animations[index],
      builder: (context, child) {
        return Transform.scale(
          scale: 0.95 + (0.05 * _animations[index].value),
          child: Card(
            elevation: 2 + (2 * _animations[index].value),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            child: Container(
              padding: const EdgeInsets.all(8),
              height: 76,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: Colors.orange[50]?.withValues(alpha: 0.5 + (0.5 * _animations[index].value)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Test icon placeholder with rotation
                  Transform.rotate(
                    angle: _animations[index].value * 0.1,
                    child: Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: Colors.orange[300]?.withValues(alpha: 0.7 + (0.3 * _animations[index].value)),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Icon(
                        Icons.assignment,
                        color: Colors.orange[600],
                        size: 16,
                      ),
                    ),
                  ),
                  const SizedBox(height: 4),
                  // Title placeholder
                  Container(
                    width: 40,
                    height: 14,
                    decoration: BoxDecoration(
                      color: Colors.orange[300]?.withValues(alpha: 0.7 + (0.3 * _animations[index].value)),
                      borderRadius: BorderRadius.circular(3),
                    ),
                  ),
                  const SizedBox(height: 3),
                  // Subtitle placeholder
                  Container(
                    width: 55,
                    height: 8,
                    decoration: BoxDecoration(
                      color: Colors.orange[300]?.withValues(alpha: 0.7 + (0.3 * _animations[index].value)),
                      borderRadius: BorderRadius.circular(3),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}