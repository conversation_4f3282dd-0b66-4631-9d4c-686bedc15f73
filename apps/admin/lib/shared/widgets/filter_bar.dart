// lib/shared/widgets/filter_bar.dart

import 'package:admin/shared/utils/string_extensions.dart';
import 'package:entities/question_entity.dart';
import 'package:entities/test_enums.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/filter_state.dart';
import '../providers/filter_helpers_provider.dart';
import '../providers/filter_provider.dart';

class FilterBar extends ConsumerWidget {
  const FilterBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final filter = ref.watch(filterProvider);

    return LayoutBuilder(
      builder: (context, constraints) {
        // If there's enough space, use expanded layout
        if (constraints.maxWidth > 800) {
          return _buildExpandedLayout(context, ref, filter);
        } else {
          return _buildCompactLayout(context, ref, filter);
        }
      },
    );
  }

  Widget _buildExpandedLayout(
      BuildContext context, WidgetRef ref, FilterState filter) {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          // Course dropdown
          Expanded(
            flex: 3,
            child: FilterField(
              label: 'Course',
              isRequired: true,
              child: AsyncDropdown(
                asyncValue: ref.watch(availableCoursesProvider),
                hint: 'Select Course',
                value: filter.courseId,
                onChanged: (val) =>
                    ref.read(filterProvider.notifier).setCourse(val),
                itemBuilder: (entry) => DropdownMenuItem(
                  value: entry.key,
                  child: Text(entry.key),
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),

          // Year dropdown
          Expanded(
            flex: 3,
            child: FilterField(
              label: 'Year',
              isRequired: true,
              child: AsyncDropdown(
                asyncValue: ref.watch(availableYearsProvider),
                hint: 'Select Year',
                value: filter.yearId,
                onChanged: (val) =>
                    ref.read(filterProvider.notifier).setYear(val),
                itemBuilder: (entry) => DropdownMenuItem(
                  value: entry.key,
                  child: Text(entry.key),
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),

          // Subject dropdown
          Expanded(
            flex: 3,
            child: FilterField(
              label: 'Subject',
              isRequired: true,
              child: AsyncDropdown(
                asyncValue: ref.watch(availableSubjectsProvider),
                hint: 'Select Subject',
                value: filter.subjectId,
                onChanged: (val) =>
                    ref.read(filterProvider.notifier).setSubject(val),
                itemBuilder: (entry) => DropdownMenuItem(
                  value: entry.key,
                  child: Text(entry.value.name),
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),

          // Topic dropdown
          Expanded(
            flex: 2,
            child: FilterField(
              label: 'Topic',
              child: Consumer(
                builder: (context, ref, child) {
                  final topicsAsync = ref.watch(availableTopicsProvider);

                  return topicsAsync.when(
                    data: (topics) => DropdownButtonFormField<String>(
                      decoration: InputDecoration(
                        hintText: topics.isEmpty ? 'No topics yet' : 'Select Topic',
                        contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                      ),
                      isExpanded: true,
                      value: filter.topic,
                      onChanged: topics.isEmpty ? null : (value) => ref.read(filterProvider.notifier).setTopic(value),
                      items: topics.isEmpty
                        ? []
                        : topics.map((topic) => DropdownMenuItem(
                            value: topic,
                            child: Text(
                              topic,
                              overflow: TextOverflow.ellipsis,
                            ),
                          )).toList(),
                    ),
                    loading: () => DropdownButtonFormField<String>(
                      decoration: const InputDecoration(
                        hintText: 'Loading...',
                        contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                      ),
                      items: const [],
                      onChanged: null,
                    ),
                    error: (error, stack) => DropdownButtonFormField<String>(
                      decoration: const InputDecoration(
                        hintText: 'Error',
                        contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                      ),
                      items: const [],
                      onChanged: null,
                    ),
                  );
                },
              ),
            ),
          ),
          const SizedBox(width: 16),

          // Type dropdown
          Expanded(
            flex: 2,
            child: FilterField(
              label: 'Type',
              child: DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  hintText: 'Select Type',
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                ),
                isExpanded: true,
                value: filter.type?.name,
                onChanged: (value) {
                  if (value != null) {
                    final typeEnum = QuestionType.values.firstWhere(
                      (e) => e.name == value,
                      orElse: () => QuestionType.mcq,
                    );
                    ref.read(filterProvider.notifier).setType(typeEnum);
                  } else {
                    ref.read(filterProvider.notifier).setType(null);
                  }
                },
                items: QuestionType.values
                    .map((type) => DropdownMenuItem(
                          value: type.name,
                          child: Text(
                            type.name.toDisplayName(),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ))
                    .toList(),
              ),
            ),
          ),
          const SizedBox(width: 16),

          // Difficulty dropdown
          Expanded(
            flex: 2,
            child: FilterField(
              label: 'Difficulty',
              child: DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  hintText: 'Select Difficulty',
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                ),
                isExpanded: true,
                value: filter.difficulty?.name,
                onChanged: (value) {
                  if (value != null) {
                    final difficultyEnum = TestDifficulty.values.firstWhere(
                      (e) => e.name == value,
                      orElse: () => TestDifficulty.easy,
                    );
                    ref
                        .read(filterProvider.notifier)
                        .setDifficulty(difficultyEnum);
                  } else {
                    ref.read(filterProvider.notifier).setDifficulty(null);
                  }
                },
                items: TestDifficulty.values
                    .map((difficulty) => DropdownMenuItem(
                          value: difficulty.name,
                          child: Text(
                            difficulty.name.toCapitalized(),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ))
                    .toList(),
              ),
            ),
          ),
          const SizedBox(width: 16),

          // Status dropdown
          Expanded(
            flex: 2,
            child: FilterField(
              label: 'Status',
              child: DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  hintText: 'Select Status',
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                ),
                isExpanded: true,
                value: filter.status?.name,
                onChanged: (value) {
                  if (value != null) {
                    final statusEnum = TestStatus.values.firstWhere(
                      (e) => e.name == value,
                      orElse: () => TestStatus.draft,
                    );
                    ref.read(filterProvider.notifier).setStatus(statusEnum);
                  } else {
                    ref.read(filterProvider.notifier).setStatus(null);
                  }
                },
                items: TestStatus.values
                    .map((status) => DropdownMenuItem(
                          value: status.name,
                          child: Text(
                            status.name.toCapitalized(),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ))
                    .toList(),
              ),
            ),
          ),

          const SizedBox(width: 16),

          // Reset button
          SizedBox(
            width: 100,
            child: FilterField(
              label: '', // Empty label to maintain consistent spacing
              child: ElevatedButton.icon(
                onPressed: () {
                  ref.read(filterProvider.notifier).reset();
                },
                icon: const Icon(Icons.refresh, size: 16),
                label: const Text('Reset'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey[200],
                  foregroundColor: Colors.black87,
                  minimumSize: const Size(double.infinity, 54),
                  // Match dropdown height exactly
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompactLayout(
      BuildContext context, WidgetRef ref, FilterState filter) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: IntrinsicHeight(
        child: Row(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            // Course dropdown
            SizedBox(
              width: 130,
              child: FilterField(
                label: 'Course',
                isRequired: true,
                child: AsyncDropdown(
                  asyncValue: ref.watch(availableCoursesProvider),
                  hint: 'Select Course',
                  value: filter.courseId,
                  onChanged: (val) =>
                      ref.read(filterProvider.notifier).setCourse(val),
                  itemBuilder: (entry) => DropdownMenuItem(
                    value: entry.key,
                    child: Text(entry.key),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),

            // Year dropdown
            SizedBox(
              width: 100,
              child: FilterField(
                label: 'Year',
                isRequired: true,
                child: AsyncDropdown(
                  asyncValue: ref.watch(availableYearsProvider),
                  hint: 'Select Year',
                  value: filter.yearId,
                  onChanged: (val) =>
                      ref.read(filterProvider.notifier).setYear(val),
                  itemBuilder: (entry) => DropdownMenuItem(
                    value: entry.key,
                    child: Text(entry.key),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),

            // Subject dropdown
            SizedBox(
              width: 130,
              child: FilterField(
                label: 'Subject',
                isRequired: true,
                child: AsyncDropdown(
                  asyncValue: ref.watch(availableSubjectsProvider),
                  hint: 'Select Subject',
                  value: filter.subjectId,
                  onChanged: (val) =>
                      ref.read(filterProvider.notifier).setSubject(val),
                  itemBuilder: (entry) => DropdownMenuItem(
                    value: entry.key,
                    child: Text(entry.value.name),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),

            // Type dropdown
            SizedBox(
              width: 100,
              child: FilterField(
                label: 'Type',
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    hintText: 'Select Type',
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                  ),
                  isExpanded: true,
                  value: filter.type?.name,
                  onChanged: (value) {
                    if (value != null) {
                      final typeEnum = QuestionType.values.firstWhere(
                        (e) => e.name == value,
                        orElse: () => QuestionType.mcq,
                      );
                      ref.read(filterProvider.notifier).setType(typeEnum);
                    } else {
                      ref.read(filterProvider.notifier).setType(null);
                    }
                  },
                  items: QuestionType.values
                      .map((type) => DropdownMenuItem(
                            value: type.name,
                            child: Text(
                              type.name.toDisplayName(),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ))
                      .toList(),
                ),
              ),
            ),
            const SizedBox(width: 12),

            // Difficulty dropdown
            SizedBox(
              width: 100,
              child: FilterField(
                label: 'Difficulty',
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    hintText: 'Select Difficulty',
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                  ),
                  isExpanded: true,
                  value: filter.difficulty?.name,
                  onChanged: (value) {
                    if (value != null) {
                      final difficultyEnum =
                          TestDifficulty.values.firstWhere(
                        (e) => e.name == value,
                        orElse: () => TestDifficulty.easy,
                      );
                      ref
                          .read(filterProvider.notifier)
                          .setDifficulty(difficultyEnum);
                    } else {
                      ref.read(filterProvider.notifier).setDifficulty(null);
                    }
                  },
                  items: TestDifficulty.values
                      .map((difficulty) => DropdownMenuItem(
                            value: difficulty.name,
                            child: Text(
                              difficulty.name.toCapitalized(),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ))
                      .toList(),
                ),
              ),
            ),
            const SizedBox(width: 12),

            // Status dropdown
            SizedBox(
              width: 100,
              child: FilterField(
                label: 'Status',
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    hintText: 'Select Status',
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                  ),
                  isExpanded: true,
                  value: filter.status?.name,
                  onChanged: (value) {
                    if (value != null) {
                      final statusEnum = TestStatus.values.firstWhere(
                        (e) => e.name == value,
                        orElse: () => TestStatus.draft,
                      );
                      ref.read(filterProvider.notifier).setStatus(statusEnum);
                    } else {
                      ref.read(filterProvider.notifier).setStatus(null);
                    }
                  },
                  items: TestStatus.values
                      .map((status) => DropdownMenuItem(
                            value: status.name,
                            child: Text(
                              status.name.toCapitalized(),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ))
                      .toList(),
                ),
              ),
            ),

            const SizedBox(width: 12),

            // Reset button
            SizedBox(
              width: 100,
              child: FilterField(
                label: '', // Empty label to maintain consistent spacing
                child: ElevatedButton.icon(
                  onPressed: () {
                    ref.read(filterProvider.notifier).reset();
                  },
                  icon: const Icon(Icons.refresh, size: 16),
                  label: const Text('Reset'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey[200],
                    foregroundColor: Colors.black87,
                    minimumSize: const Size(double.infinity, 56),
                    // Match dropdown height exactly
                    padding: const EdgeInsets.symmetric(
                        horizontal: 12, vertical: 16),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class FilterField extends StatelessWidget {
  final String label;
  final Widget child;
  final bool isRequired;

  const FilterField({
    super.key,
    required this.label,
    required this.child,
    this.isRequired = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
            if (isRequired)
              Text(
                ' *',
                style: TextStyle(
                    color: Colors.red[700], fontWeight: FontWeight.bold),
              ),
          ],
        ),
        const SizedBox(height: 8),
        child,
      ],
    );
  }
}

class AsyncDropdown extends ConsumerWidget {
  final AsyncValue<Map<String, dynamic>> asyncValue;
  final String hint;
  final String? value;
  final ValueChanged<String?> onChanged;
  final DropdownMenuItem<String> Function(MapEntry<String, dynamic>)
      itemBuilder;

  const AsyncDropdown({
    super.key,
    required this.asyncValue,
    required this.hint,
    required this.value,
    required this.onChanged,
    required this.itemBuilder,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (asyncValue.isLoading) {
      return const SizedBox(
        height: 48,
        child: Center(child: CircularProgressIndicator(strokeWidth: 2)),
      );
    }

    if (asyncValue.hasError) {
      return const SizedBox(
        height: 48,
        child: Center(
          child: Text(
            'Unable to load data',
            style: TextStyle(color: Colors.red),
          ),
        ),
      );
    }

    final data = asyncValue.value ?? {};
    final items = data.entries.map(itemBuilder).toList();

    return DropdownButtonFormField<String>(
      decoration: InputDecoration(
        hintText: hint,
        contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      ),
      isExpanded: true,
      value: value,
      onChanged: onChanged,
      items: items.map((item) {
        return DropdownMenuItem<String>(
          value: item.value,
          child: Text(
            item.child is Text
                ? (item.child as Text).data ?? ''
                : item.value ?? '',
            overflow: TextOverflow.ellipsis,
          ),
        );
      }).toList(),
    );
  }
}
