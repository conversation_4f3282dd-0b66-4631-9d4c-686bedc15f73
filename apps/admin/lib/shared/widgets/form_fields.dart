import 'package:flutter/material.dart';


class EmailFormField extends StatelessWidget {
  const EmailFormField({
    super.key,
    required this.controller,
    this.focusNode,
    this.onFieldSubmitted,
    this.labelText = 'Email',
    this.hintText = 'Enter your email',
    this.validator,
    this.enabled = true,
  });

  final TextEditingController controller;
  final FocusNode? focusNode;
  final VoidCallback? onFieldSubmitted;
  final String labelText;
  final String hintText;
  final String? Function(String?)? validator;
  final bool enabled;

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      focusNode: focusNode,
      keyboardType: TextInputType.emailAddress,
      textInputAction: TextInputAction.next,
      autofillHints: const [AutofillHints.email],
      enabled: enabled,
      decoration: InputDecoration(
        labelText: labelText,
        hintText: hintText,
        prefixIcon: const Icon(Icons.email_outlined),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      validator: validator ?? _defaultEmailValidator,
      onFieldSubmitted: onFieldSubmitted != null ? (_) => onFieldSubmitted!() : null,
    );
  }

  static String? _defaultEmailValidator(String? value) {
    if (value?.trim().isEmpty ?? true) {
      return 'Email is required';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value!)) {
      return 'Please enter a valid email';
    }
    return null;
  }
}

class PasswordFormField extends StatefulWidget {
  const PasswordFormField({
    super.key,
    required this.controller,
    this.focusNode,
    this.onFieldSubmitted,
    this.labelText = 'Password',
    this.hintText = 'Enter your password',
    this.validator,
    this.enabled = true,
    this.isConfirmPassword = false,
  });

  final TextEditingController controller;
  final FocusNode? focusNode;
  final VoidCallback? onFieldSubmitted;
  final String labelText;
  final String hintText;
  final String? Function(String?)? validator;
  final bool enabled;
  final bool isConfirmPassword;

  @override
  State<PasswordFormField> createState() => _PasswordFormFieldState();
}

class _PasswordFormFieldState extends State<PasswordFormField> {
  bool _obscureText = true;

  void _toggleVisibility() {
    setState(() {
      _obscureText = !_obscureText;
    });
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: widget.controller,
      focusNode: widget.focusNode,
      obscureText: _obscureText,
      textInputAction: TextInputAction.done,
      autofillHints: widget.isConfirmPassword 
          ? const [AutofillHints.newPassword]
          : const [AutofillHints.password],
      enabled: widget.enabled,
      decoration: InputDecoration(
        labelText: widget.labelText,
        hintText: widget.hintText,
        prefixIcon: const Icon(Icons.lock_outlined),
        suffixIcon: IconButton(
          icon: Icon(
            _obscureText ? Icons.visibility : Icons.visibility_off,
          ),
          onPressed: _toggleVisibility,
          tooltip: _obscureText ? 'Show password' : 'Hide password',
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      validator: widget.validator ?? _defaultPasswordValidator,
      onFieldSubmitted: widget.onFieldSubmitted != null ? (_) => widget.onFieldSubmitted!() : null,
    );
  }

  static String? _defaultPasswordValidator(String? value) {
    if (value?.isEmpty ?? true) {
      return 'Password is required';
    }
    if (value!.length < 6) {
      return 'Password must be at least 6 characters';
    }
    return null;
  }
}

class TextFormFieldStandard extends StatelessWidget {
  const TextFormFieldStandard({
    super.key,
    required this.controller,
    required this.labelText,
    this.focusNode,
    this.hintText,
    this.prefixIcon,
    this.suffixIcon,
    this.keyboardType,
    this.textInputAction,
    this.validator,
    this.onFieldSubmitted,
    this.enabled = true,
    this.maxLines = 1,
    this.autofillHints,
  });

  final TextEditingController controller;
  final String labelText;
  final FocusNode? focusNode;
  final String? hintText;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final String? Function(String?)? validator;
  final VoidCallback? onFieldSubmitted;
  final bool enabled;
  final int maxLines;
  final List<String>? autofillHints;

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      focusNode: focusNode,
      keyboardType: keyboardType,
      textInputAction: textInputAction,
      autofillHints: autofillHints,
      enabled: enabled,
      maxLines: maxLines,
      decoration: InputDecoration(
        labelText: labelText,
        hintText: hintText,
        prefixIcon: prefixIcon,
        suffixIcon: suffixIcon,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      validator: validator,
      onFieldSubmitted: onFieldSubmitted != null ? (_) => onFieldSubmitted!() : null,
    );
  }
}

class PhoneFormField extends StatelessWidget {
  const PhoneFormField({
    super.key,
    required this.controller,
    this.focusNode,
    this.onFieldSubmitted,
    this.labelText = 'Phone Number',
    this.hintText = 'Enter your phone number',
    this.validator,
    this.enabled = true,
  });

  final TextEditingController controller;
  final FocusNode? focusNode;
  final VoidCallback? onFieldSubmitted;
  final String labelText;
  final String hintText;
  final String? Function(String?)? validator;
  final bool enabled;

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      focusNode: focusNode,
      keyboardType: TextInputType.phone,
      textInputAction: TextInputAction.next,
      autofillHints: const [AutofillHints.telephoneNumber],
      enabled: enabled,
      decoration: InputDecoration(
        labelText: labelText,
        hintText: hintText,
        prefixIcon: const Icon(Icons.phone_outlined),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      validator: validator ?? _defaultPhoneValidator,
      onFieldSubmitted: onFieldSubmitted != null ? (_) => onFieldSubmitted!() : null,
    );
  }

  static String? _defaultPhoneValidator(String? value) {
    if (value?.trim().isEmpty ?? true) {
      return 'Phone number is required';
    }
    return null;
  }
}
