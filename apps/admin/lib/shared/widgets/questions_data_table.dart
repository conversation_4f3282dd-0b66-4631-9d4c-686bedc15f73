// lib/shared/widgets/questions_data_table.dart

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:entities/question_entity.dart';
import 'package:firebase_ui_firestore/firebase_ui_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../features/questions/widgets/question_status_badge.dart';
import '../utils/string_extensions.dart';
import 'app_button.dart';
import 'question_image_cell.dart';
import 'status_messages.dart';

/// Display mode for the questions data table
enum QuestionsDisplayMode {
  /// Display mode for viewing questions with action buttons
  view,

  /// Display mode for selecting questions with checkboxes
  select,
}

/// A reusable widget for displaying questions in a data table.
///
/// This widget can be used in two modes:
/// - View mode: Shows questions with action buttons (for Question Screen)
/// - Select mode: Shows questions with checkboxes (for Questions Section)
class QuestionsDataTable extends ConsumerWidget {
  /// The Firestore query for fetching questions
  final Query<QuestionEntity> query;

  /// The display mode (view or select)
  final QuestionsDisplayMode displayMode;

  /// The currently selected question IDs (only used in select mode)
  final List<String>? selectedIds;

  /// Callback when a question is selected/deselected (only used in select mode)
  final Function(String questionId, bool selected)? onQuestionToggle;

  /// Callback to render action buttons (only used in view mode)
  final Widget Function(QuestionEntity question)? actionBuilder;

  /// Optional title to display above the table
  final String? title;

  /// Optional subtitle to display above the table
  final String? subtitle;

  /// Whether to show the difficulty column
  final bool showDifficulty;

  /// Whether to show the status column
  final bool showStatus;

  /// Whether to show the type column
  final bool showType;

  /// Whether to show the answer column (for test creation)
  final bool showAnswer;

  /// Callback when all questions are selected/deselected (only used in select mode)
  final Function(List<String> questionIds)? onSelectAll;

  /// Callback when a specific number of questions are selected (only used in select mode)
  final Function(List<String> questionIds)? onSelectCount;

  const QuestionsDataTable({
    required this.query,
    required this.displayMode,
    this.selectedIds,
    this.onQuestionToggle,
    this.actionBuilder,
    this.title,
    this.subtitle,
    this.showDifficulty = true,
    this.showStatus = true,
    this.showType = true,
    this.showAnswer = false,
    this.onSelectAll,
    this.onSelectCount,
    super.key,
  }) : assert(
          (displayMode == QuestionsDisplayMode.select &&
                  selectedIds != null &&
                  onQuestionToggle != null) ||
              (displayMode == QuestionsDisplayMode.view &&
                  actionBuilder != null),
          'For select mode, selectedIds and onQuestionToggle must be provided. For view mode, actionBuilder must be provided.',
        );

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return FirestoreQueryBuilder<QuestionEntity>(
      query: query,
      pageSize: 20,
      builder: (context, snapshot, _) {
        // Handle loading state
        if (snapshot.isFetching && snapshot.docs.isEmpty) {
          return const LoadingMessage(message: "Loading questions...");
        }

        // Handle error state
        if (snapshot.hasError) {
          debugPrint(
              "There was a problem retrieving the questions: ${snapshot.error}");
          return ErrorMessage(
            title: "Error Loading Questions",
            message: "There was a problem retrieving the questions.",
            onRetry: () => snapshot.fetchMore(),
          );
        }

        // Handle empty state
        if (snapshot.docs.isEmpty) {
          return const EmptyDataMessage(
            title: "No Questions Found",
            message:
                "No questions match your current filters. Try changing your filter criteria or add a new question.",
          );
        }

        // Get questions from snapshot
        final questions = snapshot.docs.map((doc) => doc.data()).toList();

        // Build columns based on display mode
        final columns = _buildColumns();

        // Build rows from questions
        final rows = _buildRows(context, questions);

        // Fetch more when needed
        if (snapshot.hasMore && !snapshot.isFetching) {
          Future.delayed(Duration.zero, () {
            snapshot.fetchMore();
          });
        }

        // Return the data table with optional title and pagination
        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Title and subtitle if provided
            if (title != null) ...[
              Text(
                title!,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
            ],

            // Subtitle or count if in select mode
            if (subtitle != null ||
                displayMode == QuestionsDisplayMode.select) ...[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    subtitle ?? 'Available questions: ${questions.length}',
                    style: const TextStyle(
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                  if (displayMode == QuestionsDisplayMode.select && onSelectAll != null) ...[
                    Row(
                      children: [
                        // Select All button
                        TextButton(
                          onPressed: () {
                            final allQuestionIds = questions.map((q) => q.id!).toList();
                            final allSelected = selectedIds!.length == questions.length;
                            onSelectAll!(allSelected ? [] : allQuestionIds);
                          },
                          child: Text(
                            selectedIds!.length == questions.length ? 'Deselect All' : 'Select All',
                          ),
                        ),

                        // Select specific number
                        if (onSelectCount != null) ...[
                          const SizedBox(width: 16),
                          SizedBox(
                            width: 60,
                            height: 36,
                            child: TextField(
                              decoration: const InputDecoration(
                                contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                                hintText: '#',
                                border: OutlineInputBorder(),
                              ),
                              keyboardType: TextInputType.number,
                              onSubmitted: (value) {
                                final count = int.tryParse(value);
                                if (count != null && count > 0 && count <= questions.length) {
                                  final questionIds = questions.take(count).map((q) => q.id!).toList();
                                  onSelectCount!(questionIds);
                                }
                              },
                            ),
                          ),
                          const SizedBox(width: 8),
                          TextButton(
                            onPressed: () {
                              // Show a dialog to explain how to use this feature
                              showDialog(
                                context: context,
                                builder: (context) => AlertDialog(
                                  title: const Text('Select Questions'),
                                  content: const Text(
                                    'Enter a number to select that many questions from the top of the list. '
                                    'For example, entering "10" will select the first 10 questions.'
                                  ),
                                  actions: [
                                    TextButton(
                                      onPressed: () => Navigator.of(context).pop(),
                                      child: const Text('OK'),
                                    ),
                                  ],
                                ),
                              );
                            },
                            child: const Text('Help'),
                          ),
                        ],
                      ],
                    ),
                  ],
                ],
              ),
              const SizedBox(height: 16),
            ],

            // Data table
            Expanded(
              child: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Theme(
                    data: Theme.of(context).copyWith(
                      dataTableTheme: DataTableTheme.of(context).copyWith(
                        headingRowColor:
                            WidgetStateProperty.all(const Color(0xFFE6E9FF)),
                        dataRowMinHeight: 60,
                        dataRowMaxHeight: 60,
                        headingRowHeight: 48,
                        columnSpacing: 16,
                      ),
                    ),
                    child: DataTable(
                      columns: columns,
                      rows: rows,
                      horizontalMargin: 16,
                      showCheckboxColumn: displayMode == QuestionsDisplayMode.select,
                    ),
                  ),
                ),
              ),
            ),

            // Pagination section with AppButton - only shown when more data is available
            if (snapshot.hasMore ||
                (snapshot.isFetching && snapshot.docs.isNotEmpty))
              Center(
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16.0),
                  child: snapshot.isFetching
                      ? const AppButton(
                          label: 'Loading...',
                          onPressed: null,
                          isLoading: true,
                          variant: AppButtonVariant.outlined,
                          width: 200,
                        )
                      : AppButton(
                          label: 'Load More',
                          onPressed: () => snapshot.fetchMore(),
                          variant: AppButtonVariant.outlined,
                          width: 200,
                        ),
                ),
              ),
          ],
        );
      },
    );
  }

  // Build columns based on display mode and configuration
  List<DataColumn> _buildColumns() {
    final List<DataColumn> columns = [];

    // Selection column for select mode
    if (displayMode == QuestionsDisplayMode.select) {
      columns.add(const DataColumn(label: Text('Select')));
    }

    // Common columns
    columns.add(const DataColumn(label: Text('Image')));
    columns.add(const DataColumn(label: Text('Question')));

    // Answer column for test creation
    if (showAnswer) {
      columns.add(const DataColumn(label: Text('Answer')));
    }

    // Optional columns
    if (showType) {
      columns.add(const DataColumn(label: Text('Type')));
    }

    if (showDifficulty) {
      columns.add(const DataColumn(label: Text('Difficulty')));
    }

    if (showStatus) {
      columns.add(const DataColumn(label: Text('Status')));
    }

    // Action column for view mode
    if (displayMode == QuestionsDisplayMode.view) {
      columns.add(const DataColumn(label: Text('Actions')));
    }

    return columns;
  }

  // Build rows from questions
  List<DataRow> _buildRows(BuildContext context, List<QuestionEntity> questions) {
    // Log selected IDs for debugging
    if (displayMode == QuestionsDisplayMode.select && selectedIds != null) {
      debugPrint('Building rows with ${selectedIds!.length} selected questions');
    }

    return questions.map((question) {
      final cells = <DataCell>[];

      // Selection cell for select mode
      if (displayMode == QuestionsDisplayMode.select) {
        final isSelected = selectedIds!.contains(question.id);

        // Log selection status for debugging
        if (isSelected) {
          debugPrint('Question ${question.id} is selected');
        }

        cells.add(
          DataCell(
            Checkbox(
              value: isSelected,
              onChanged: (value) {
                if (value != null) {
                  onQuestionToggle!(question.id!, value);
                }
              },
            ),
          ),
        );
      }

      // Common cells
      cells.add(DataCell(QuestionImageCell(imagePath: question.questionImage)));
      cells.add(
        DataCell(
          Tooltip(
            message: question.question,
            child: Text(
              question.question,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
        ),
      );

      // Answer cell for test creation
      if (showAnswer) {
        String answerText = '';
        if (question.type == QuestionType.mcq) {
          // For MCQ, show the correct option
          final correctIndex = question.correctOptionIndex;
          if (correctIndex >= 0 && correctIndex < question.options.length) {
            final optionLabel = String.fromCharCode(65 + correctIndex); // A, B, C, D
            answerText = '$optionLabel. ${question.options[correctIndex]}';
          }
        } else if (question.type == QuestionType.flipCard) {
          // For flip card, show the answer
          answerText = question.answer ?? '';
        }

        cells.add(
          DataCell(
            Tooltip(
              message: answerText,
              child: Text(
                answerText,
                overflow: TextOverflow.ellipsis,
                maxLines: 2,
              ),
            ),
          ),
        );
      }

      // Optional cells
      if (showType) {
        cells.add(DataCell(
            Text(question.type.toString().split('.').last.toCapitalized())));
      }

      if (showDifficulty) {
        cells.add(DataCell(Text('N/A'))); // Difficulty removed from questions
      }

      if (showStatus) {
        cells.add(DataCell(QuestionStatusBadge())); // Status removed from questions
      }

      // Action cell for view mode
      if (displayMode == QuestionsDisplayMode.view) {
        cells.add(DataCell(actionBuilder!(question)));
      }

      return DataRow(
        cells: cells,
        onSelectChanged: displayMode == QuestionsDisplayMode.view
          ? (_) {
              // Navigate to question view screen
              if (question.id != null) {
                context.push('/view-question/${question.id}');
              }
            }
          : null,
      );
    }).toList();
  }
}
