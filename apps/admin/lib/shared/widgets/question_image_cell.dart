import 'package:cached_network_image/cached_network_image.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';

class QuestionImageCell extends StatelessWidget {
  final String? imagePath;
  final double width;
  final double height;

  const QuestionImageCell({
    this.imagePath,
    this.width = 40,
    this.height = 40,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    if (imagePath == null || imagePath!.isEmpty) {
      return Container(
        width: width,
        height: height,
        color: Colors.grey[300],
        child: Icon(Icons.image, size: width / 2),
      );
    }

    return SizedBox(
      width: width + 10,
      height: height + 10,
      child: FutureBuilder<String>(
        future: FirebaseStorage.instance.ref(imagePath).getDownloadURL(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Container(
              width: width,
              height: height,
              color: Colors.grey[200],
              child: Center(
                child: SizedBox(
                  width: width / 2,
                  height: height / 2,
                  child: const CircularProgressIndicator(
                    strokeWidth: 2,
                  ),
                ),
              ),
            );
          }

          if (snapshot.hasError || !snapshot.hasData) {
            return Container(
              width: width,
              height: height,
              color: Colors.grey[300],
              child: Icon(Icons.error_outline, size: width / 2),
            );
          }

          return ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: CachedNetworkImage(
              imageUrl: snapshot.data!,
              fit: BoxFit.cover,
              width: width,
              height: height,
              placeholder: (context, url) => Container(
                width: width,
                height: height,
                color: Colors.grey[200],
                child: Center(
                  child: SizedBox(
                    width: width / 2,
                    height: height / 2,
                    child: const CircularProgressIndicator(
                      strokeWidth: 2,
                    ),
                  ),
                ),
              ),
              errorWidget: (context, url, error) => Container(
                width: width,
                height: height,
                color: Colors.grey[300],
                child: Icon(Icons.error_outline, size: width / 2),
              ),
            ),
          );
        },
      ),
    );
  }
}
