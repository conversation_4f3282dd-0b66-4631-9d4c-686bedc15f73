
import 'package:admin/shared/widgets/app_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'filter_bar.dart';

class BaseContentScreen extends ConsumerWidget {
  final String title;
  final String? addButtonText; // Made optional for role-based access
  final VoidCallback? onAdd; // Made optional for role-based access
  final Widget dataTable;
  final Widget? statsCards;
  final bool showFilterBar;
  final String? totalCount; // For dynamic title with count

  const BaseContentScreen({
    super.key,
    required this.title,
    this.addButtonText, // Optional - only show if user has permission
    this.onAdd, // Optional - only show if user has permission
    required this.dataTable,
    this.statsCards,
    this.showFilterBar = true,
    this.totalCount,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                totalCount != null ? '$title $totalCount' : title,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (addButtonText != null && onAdd != null)
                AppButton(
                  label: addButtonText!,
                  onPressed: onAdd!,
                  icon: Icons.add,
                  variant: AppButtonVariant.filled,
                ),
            ],
          ),
          const SizedBox(height: 16),
          if (statsCards != null) ...[
            statsCards!,
            const SizedBox(height: 16),
          ],
          Expanded(
            child: Card(
              color: Colors.white,
              elevation: 2,
              margin: EdgeInsets.zero,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  if (showFilterBar)
                    const Padding(
                      padding: EdgeInsets.all(16.0),
                      child: FilterBar(),
                    ),
                  Expanded(
                    child: dataTable,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
