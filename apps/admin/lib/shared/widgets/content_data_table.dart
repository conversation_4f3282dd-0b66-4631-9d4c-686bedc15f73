// lib/features/common/widgets/content_data_table.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class ContentDataTable extends ConsumerWidget {
  final List<DataColumn> columns;
  final List<DataRow> rows;
  final bool isLoading;

  const ContentDataTable({
    super.key,
    required this.columns,
    required this.rows,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (rows.isEmpty) {
      return Center(
        child: Text(
          "No data available",
          style: const TextStyle(
            fontSize: 16,
            color: Colors.grey,
          ),
        ),
      );
    }

    return SingleChildScrollView(
      child: Theme(
        data: Theme.of(context).copyWith(
          dataTableTheme: DataTableTheme.of(context).copyWith(
            headingRowColor: WidgetStateProperty.all(const Color(0xFFE6E9FF)),
            dataRowMinHeight: 60,
            dataRowMaxHeight: 60,
            headingRowHeight: 48,
            columnSpacing: 16,
          ),
        ),
        child: DataTable(
          columns: columns,
          rows: rows,
          horizontalMargin: 16,
        ),
      ),
    );
  }
}
