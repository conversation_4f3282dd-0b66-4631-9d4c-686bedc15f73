// lib/shared/utils/string_extensions.dart

extension StringExtension on String {
  String toCapitalized() =>
      length > 0 ? '${this[0].toUpperCase()}${substring(1)}' : '';

  String capitalize() =>
      length > 0 ? '${this[0].toUpperCase()}${substring(1)}' : '';

  String toCamelCase() {
    if (isEmpty) return this;
    return '${this[0].toLowerCase()}${substring(1)}';
  }

  String toSnakeCase() {
    if (isEmpty) return this;
    return replaceAllMapped(
      RegExp(r'(?<!^)[A-Z]'),
      (match) => '_${match.group(0)?.toLowerCase()}',
    ).toLowerCase();
  }

  String fromSnakeCase() {
    if (isEmpty) return this;
    return split('_').map((word) => word.toCapitalized()).join();
  }

  /// Convert camelCase to proper display format
  /// Example: "flipCard" -> "Flip Card"
  String toDisplayName() {
    if (isEmpty) return this;

    // Handle special cases for professional display
    switch (this) {
      case 'flipCard':
        return 'Flip Card';
      case 'mcq':
        return 'MCQ';
      default:
        // Convert camelCase to space-separated words
        return replaceAllMapped(
          RegExp(r'(?<!^)(?=[A-Z])'),
          (match) => ' ',
        ).toCapitalized();
    }
  }
}
