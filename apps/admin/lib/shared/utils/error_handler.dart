// apps/admin/lib/shared/utils/error_handler.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:providers/common.dart';
import 'package:providers/scaffold_messenger_key_provider.dart';

/// Comprehensive error handler for admin panel with user-friendly messages
class ErrorHandler {
  /// Handle authentication errors with admin-specific context
  static String getAuthErrorMessage(dynamic error) {
    dbgPrint('Admin auth error: $error');
    final errorString = error.toString().toLowerCase();

    // Admin-specific authentication errors
    if (errorString.contains('admin') || errorString.contains('privilege')) {
      if (errorString.contains('denied') ||
          errorString.contains('unauthorized')) {
        return 'Access denied. You need admin privileges to access this panel.';
      } else if (errorString.contains('revoked')) {
        return 'Your admin privileges have been revoked. Please contact support.';
      } else if (errorString.contains('expired')) {
        return 'Your admin session has expired. Please sign in again.';
      }
    }

    // Firebase Auth specific errors
    if (errorString.contains('firebase_auth/')) {
      if (errorString.contains('user-not-found')) {
        return 'No admin account found with this email. Please check your credentials.';
      } else if (errorString.contains('user-disabled')) {
        return 'This admin account has been disabled. Please contact support.';
      } else if (errorString.contains('wrong-password')) {
        return 'Incorrect password. Please try again or reset your password.';
      } else if (errorString.contains('invalid-credential')) {
        return 'Invalid admin credentials. Please check your email and password.';
      } else if (errorString.contains('invalid-email')) {
        return 'Please enter a valid email address.';
      } else if (errorString.contains('email-already-in-use')) {
        return 'An account already exists with this email address.';
      } else if (errorString.contains('weak-password')) {
        return 'Password is too weak. Please choose a stronger password.';
      } else if (errorString.contains('too-many-requests')) {
        return 'Too many failed attempts. Please try again later or reset your password.';
      } else if (errorString.contains('network-request-failed')) {
        return 'Network connection issue. Please check your internet connection.';
      } else if (errorString.contains('operation-not-allowed')) {
        return 'This sign-in method is not enabled. Please contact support.';
      } else if (errorString.contains('user-token-expired')) {
        return 'Your admin session has expired. Please sign in again.';
      }
    }

    // General authentication issues
    if (errorString.contains('user-not-found')) {
      return 'No admin account found with this email. Please check your credentials.';
    } else if (errorString.contains('wrong-password')) {
      return 'Incorrect password. Please try again or reset your password.';
    } else if (errorString.contains('invalid-credential')) {
      return 'Invalid admin credentials. Please check your email and password.';
    } else if (errorString.contains('invalid-email')) {
      return 'Please enter a valid email address.';
    } else if (errorString.contains('too-many-requests')) {
      return 'Too many failed attempts. Please try again later or reset your password.';
    } else if (errorString.contains('network')) {
      return 'Network connection issue. Please check your internet connection.';
    } else if (errorString.contains('timeout')) {
      return 'Request timed out. Please try again.';
    }

    // Default admin authentication error
    return 'Admin login failed. Please verify your credentials and try again.';
  }

  /// Handle Firestore errors with admin context
  static String getFirestoreErrorMessage(dynamic error) {
    dbgPrint('Admin Firestore error: $error');
    final errorString = error.toString().toLowerCase();

    if (errorString.contains('permission-denied')) {
      return 'Access denied. This admin operation requires higher privileges.';
    } else if (errorString.contains('not-found')) {
      return 'The requested admin resource was not found.';
    } else if (errorString.contains('already-exists')) {
      return 'This admin resource already exists.';
    } else if (errorString.contains('invalid-argument')) {
      return 'Invalid data provided. Please check your inputs and try again.';
    } else if (errorString.contains('failed-precondition')) {
      return 'Operation cannot be performed in the current state.';
    } else if (errorString.contains('resource-exhausted')) {
      return 'Resource quota exceeded. Please try again later.';
    } else if (errorString.contains('unavailable')) {
      return 'Service temporarily unavailable. Please try again later.';
    } else if (errorString.contains('deadline-exceeded')) {
      return 'Operation timed out. Please try again.';
    } else if (errorString.contains('unauthenticated')) {
      return 'Admin authentication required for this operation.';
    }

    return 'Database operation failed. Please try again.';
  }

  /// Handle storage errors with admin context
  static String getStorageErrorMessage(dynamic error) {
    dbgPrint('Admin storage error: $error');
    final errorString = error.toString().toLowerCase();

    if (errorString.contains('unauthorized') ||
        errorString.contains('not-authorized')) {
      return 'Unauthorized file operation. Please check your admin permissions.';
    } else if (errorString.contains('file-not-found')) {
      return 'File not found. It may have been moved or deleted.';
    } else if (errorString.contains('file-too-large')) {
      return 'File too large for upload. Please select a smaller file.';
    } else if (errorString.contains('invalid-file')) {
      return 'Invalid file format. Please check file requirements.';
    } else if (errorString.contains('quota-exceeded')) {
      return 'Storage quota exceeded. Please contact support.';
    } else if (errorString.contains('network-error')) {
      return 'Network error during file operation. Please check your connection.';
    }

    return 'File operation failed. Please try again.';
  }

  /// Handle Firebase Functions errors
  static String getFunctionsErrorMessage(dynamic error) {
    dbgPrint('Admin Firebase Functions error: $error');
    final errorString = error.toString();

    // Check if it's already a user-friendly Exception message
    if (errorString.startsWith('Exception: ')) {
      final message = errorString.replaceFirst('Exception: ', '');
      // If it's already user-friendly, return it
      if (message.contains('You do not have permission') ||
          message.contains('already exists') ||
          message.contains('contact your administrator') ||
          message.contains('Please') ||
          message.contains('Invalid')) {
        return message;
      }
    }

    // Extract the actual error message from Firebase Functions exceptions
    if (errorString.contains('FirebaseFunctionsException')) {
      // Try to extract the message part after the exception type
      final regex = RegExp(r'FirebaseFunctionsException.*?message:\s*(.+?)(?:\s*\(|$)');
      final match = regex.firstMatch(errorString);
      if (match != null && match.group(1) != null) {
        return match.group(1)!.trim();
      }
    }

    // Handle specific Firebase Functions error codes
    if (errorString.contains('already-exists')) {
      return 'A user with this email already exists';
    } else if (errorString.contains('invalid-argument')) {
      return 'Invalid input provided. Please check your data and try again.';
    } else if (errorString.contains('permission-denied')) {
      return 'You do not have permission to perform this operation. Please contact your administrator.';
    } else if (errorString.contains('unauthenticated')) {
      return 'Authentication required. Please log in and try again.';
    }

    // If it's a user-friendly message, preserve it
    if (errorString.contains('A user with this email already exists') ||
        errorString.contains('Password is too weak') ||
        errorString.contains('Invalid email address')) {
      return errorString.replaceFirst('Exception: ', '');
    }

    return 'Cloud function operation failed. Please try again.';
  }

  /// Handle general operation errors
  static String getGeneralErrorMessage(dynamic error) {
    dbgPrint('Admin operation error: $error');
    final errorString = error.toString();
    final lowerErrorString = errorString.toLowerCase();

    // Check if it's already a user-friendly Exception message
    if (errorString.startsWith('Exception: ')) {
      final message = errorString.replaceFirst('Exception: ', '');
      // If it's already user-friendly, return it
      if (message.contains('You do not have permission') ||
          message.contains('contact your administrator') ||
          message.contains('Please') ||
          message.contains('already exists') ||
          message.contains('Invalid') ||
          message.contains('required') ||
          message.contains('cannot be empty')) {
        return message;
      }
    }

    // Validation errors (preserve original message if user-friendly)
    if (lowerErrorString.contains('please select') ||
        lowerErrorString.contains('please provide') ||
        lowerErrorString.contains('required') ||
        lowerErrorString.contains('cannot be empty') ||
        lowerErrorString.contains('at least') ||
        lowerErrorString.contains('missing')) {
      return errorString.replaceFirst('Exception: ', '');
    }

    // User-friendly error messages (preserve them)
    if (lowerErrorString.contains('a user with this email already exists') ||
        lowerErrorString.contains('password is too weak') ||
        lowerErrorString.contains('invalid email address') ||
        lowerErrorString.contains('you do not have permission') ||
        lowerErrorString.contains('contact your administrator')) {
      return errorString.replaceFirst('Exception: ', '');
    }

    // Network and connectivity
    if (lowerErrorString.contains('network') || lowerErrorString.contains('connection')) {
      return 'Network issue. Please check your connection and try again.';
    }

    // Timeout errors
    if (lowerErrorString.contains('timeout')) {
      return 'Operation timed out. Please try again.';
    }

    return 'Operation failed. Please try again.';
  }

  /// Determine error type and get appropriate message
  static String getErrorMessage(dynamic error) {
    final errorString = error.toString().toLowerCase();

    // Determine error type and get appropriate message
    if (errorString.contains('firebasefunctionsexception') ||
        errorString.contains('cloud_functions')) {
      return getFunctionsErrorMessage(error);
    } else if (errorString.contains('firebase_auth') ||
        errorString.contains('auth/')) {
      return getAuthErrorMessage(error);
    } else if (errorString.contains('firestore') ||
        errorString.contains('cloud_firestore')) {
      return getFirestoreErrorMessage(error);
    } else if (errorString.contains('storage') ||
        errorString.contains('firebase_storage')) {
      return getStorageErrorMessage(error);
    } else {
      return getGeneralErrorMessage(error);
    }
  }

  /// Show error snackbar with user-friendly message
  static void showErrorSnackBar(WidgetRef ref, dynamic error,
      {String? context}) {
    final userMessage = getErrorMessage(error);
    final finalMessage =
        context != null ? '$context: $userMessage' : userMessage;

    showSnackBarRef(ref, finalMessage, Colors.red[700]);
  }

  /// Show success snackbar
  static void showSuccessSnackBar(WidgetRef ref, String message) {
    showSnackBarRef(ref, message, Colors.green[700]);
  }

  /// Show info snackbar
  static void showInfoSnackBar(WidgetRef ref, String message) {
    showSnackBarRef(ref, message, Colors.blue[700]);
  }

  /// Show warning snackbar
  static void showWarningSnackBar(WidgetRef ref, String message) {
    showSnackBarRef(ref, message, Colors.orange[700]);
  }

  /// Helper method to show snackbar using scaffold messenger
  static void showSnackBarRef(
      WidgetRef ref, String message, Color? backgroundColor) {
    final scaffoldMessenger =
        ref.read(scaffoldMessengerKeyProvider).currentState;
    if (scaffoldMessenger != null) {
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: backgroundColor,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 4),
          action: SnackBarAction(
            label: 'Dismiss',
            textColor: Colors.white,
            onPressed: () => scaffoldMessenger.hideCurrentSnackBar(),
          ),
        ),
      );
    }
  }
}
