// lib/shared/providers/filter_provider.dart

import 'package:entities/question_entity.dart';
import 'package:entities/test_enums.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../models/filter_state.dart';

part 'filter_provider.g.dart';

/// Provider for filter state that persists throughout app session
@Riverpod(keepAlive: true)
class Filter extends _$Filter {
  @override
  FilterState build() {
    return FilterState();
  }

  /// Set courseId without resetting other filters
  void setCourse(String? courseId) {
    // Setting course should only reset year and subject since they're hierarchical
    state = state.copyWith(
      courseId: courseId,
      yearId: null,
      subjectId: null,
      // Preserve other filters
      topic: state.topic,
      type: state.type,
      difficulty: state.difficulty,
      status: state.status,
    );
  }

  /// Set yearId without resetting non-hierarchical filters
  void setYear(String? yearId) {
    // Setting year should only reset subject since it's hierarchical
    state = state.copyWith(
      yearId: yearId,
      subjectId: null,
      // Preserve other filters
      courseId: state.courseId,
      topic: state.topic,
      type: state.type,
      difficulty: state.difficulty,
      status: state.status,
    );
  }

  /// Set subjectId without resetting any other filters
  void setSubject(String? subjectId) {
    // Setting subject shouldn't reset anything else
    state = state.copyWith(
      subjectId: subjectId,
      // Preserve all other filters
      courseId: state.courseId,
      yearId: state.yearId,
      topic: state.topic,
      type: state.type,
      difficulty: state.difficulty,
      status: state.status,
    );
  }

  /// Set topic without resetting any other filters
  void setTopic(String? topic) {
    state = state.copyWith(
      topic: topic,
      // Preserve all other filters
      courseId: state.courseId,
      yearId: state.yearId,
      subjectId: state.subjectId,
      type: state.type,
      difficulty: state.difficulty,
      status: state.status,
    );
  }

  /// Set type without resetting any other filters
  void setType(QuestionType? type) {
    state = state.copyWith(
      type: type,
      // Preserve all other filters
      courseId: state.courseId,
      yearId: state.yearId,
      subjectId: state.subjectId,
      topic: state.topic,
      difficulty: state.difficulty,
      status: state.status,
    );
  }

  /// Set difficulty without resetting any other filters
  void setDifficulty(TestDifficulty? difficulty) {
    state = state.copyWith(
      difficulty: difficulty,
      // Preserve all other filters
      courseId: state.courseId,
      yearId: state.yearId,
      subjectId: state.subjectId,
      topic: state.topic,
      type: state.type,
      status: state.status,
    );
  }

  /// Set status without resetting any other filters
  void setStatus(TestStatus? status) {
    state = state.copyWith(
      status: status,
      // Preserve all other filters
      courseId: state.courseId,
      yearId: state.yearId,
      subjectId: state.subjectId,
      topic: state.topic,
      type: state.type,
      difficulty: state.difficulty,
    );
  }

  /// Reset all filters
  void reset() {
    state = FilterState();
  }
}
