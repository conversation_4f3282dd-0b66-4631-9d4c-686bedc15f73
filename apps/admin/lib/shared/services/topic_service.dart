// apps/admin/lib/shared/services/topic_service.dart

import 'package:cloud_firestore/cloud_firestore.dart';

class TopicService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Add a new topic to a subject if it doesn't already exist
  static Future<void> addTopicToSubject({
    required String courseId,
    required String yearId,
    required String subjectId,
    required String topicName,
  }) async {
    if (topicName.trim().isEmpty) return;

    final trimmedTopic = topicName.trim();
    
    // Get the subject document path
    final subjectDocPath = 'app_config/courses';
    final docRef = _firestore.doc(subjectDocPath);

    await _firestore.runTransaction((transaction) async {
      final snapshot = await transaction.get(docRef);
      
      if (!snapshot.exists) {
        throw Exception('Courses document not found');
      }

      final data = snapshot.data()!;
      
      // Navigate to the specific subject
      final courses = data['courses'] as Map<String, dynamic>? ?? {};
      final course = courses[courseId] as Map<String, dynamic>? ?? {};
      final years = course['years'] as Map<String, dynamic>? ?? {};
      final year = years[yearId] as Map<String, dynamic>? ?? {};
      final subjects = year['subjects'] as Map<String, dynamic>? ?? {};
      final subject = subjects[subjectId] as Map<String, dynamic>? ?? {};
      
      // Get current topics list
      final currentTopics = List<String>.from(subject['topics'] ?? []);
      
      // Check if topic already exists (case-insensitive)
      final topicExists = currentTopics.any(
        (topic) => topic.toLowerCase() == trimmedTopic.toLowerCase(),
      );

      // Debug logging (can be removed in production)
      // debugPrint('TopicService: Current topics: $currentTopics');
      // debugPrint('TopicService: Adding topic: $trimmedTopic');
      // debugPrint('TopicService: Topic exists: $topicExists');

      if (!topicExists) {
        // Add the new topic
        currentTopics.add(trimmedTopic);
        currentTopics.sort(); // Keep topics sorted
        
        // Update the subject with new topics list
        final updatedSubject = {
          ...subject,
          'topics': currentTopics,
        };
        
        // Update the nested structure
        final updatedSubjects = {
          ...subjects,
          subjectId: updatedSubject,
        };
        
        final updatedYear = {
          ...year,
          'subjects': updatedSubjects,
        };
        
        final updatedYears = {
          ...years,
          yearId: updatedYear,
        };
        
        final updatedCourse = {
          ...course,
          'years': updatedYears,
        };
        
        final updatedCourses = {
          ...courses,
          courseId: updatedCourse,
        };
        
        final updatedData = {
          ...data,
          'courses': updatedCourses,
        };
        
        // Write the updated document
        transaction.update(docRef, updatedData);
      }
    });
  }

  /// Remove a topic from a subject
  static Future<void> removeTopicFromSubject({
    required String courseId,
    required String yearId,
    required String subjectId,
    required String topicName,
  }) async {
    final subjectDocPath = 'app_config/courses';
    final docRef = _firestore.doc(subjectDocPath);

    await _firestore.runTransaction((transaction) async {
      final snapshot = await transaction.get(docRef);
      
      if (!snapshot.exists) {
        throw Exception('Courses document not found');
      }

      final data = snapshot.data()!;
      
      // Navigate to the specific subject
      final courses = data['courses'] as Map<String, dynamic>? ?? {};
      final course = courses[courseId] as Map<String, dynamic>? ?? {};
      final years = course['years'] as Map<String, dynamic>? ?? {};
      final year = years[yearId] as Map<String, dynamic>? ?? {};
      final subjects = year['subjects'] as Map<String, dynamic>? ?? {};
      final subject = subjects[subjectId] as Map<String, dynamic>? ?? {};
      
      // Get current topics list and remove the topic
      final currentTopics = List<String>.from(subject['topics'] ?? []);
      currentTopics.removeWhere((topic) => topic == topicName);
      
      // Update the subject with new topics list
      final updatedSubject = {
        ...subject,
        'topics': currentTopics,
      };
      
      // Update the nested structure (same as add)
      final updatedSubjects = {
        ...subjects,
        subjectId: updatedSubject,
      };
      
      final updatedYear = {
        ...year,
        'subjects': updatedSubjects,
      };
      
      final updatedYears = {
        ...years,
        yearId: updatedYear,
      };
      
      final updatedCourse = {
        ...course,
        'years': updatedYears,
      };
      
      final updatedCourses = {
        ...courses,
        courseId: updatedCourse,
      };
      
      final updatedData = {
        ...data,
        'courses': updatedCourses,
      };
      
      // Write the updated document
      transaction.update(docRef, updatedData);
    });
  }

  /// Update a topic name in a subject
  static Future<void> updateTopicInSubject({
    required String courseId,
    required String yearId,
    required String subjectId,
    required String oldTopicName,
    required String newTopicName,
  }) async {
    if (newTopicName.trim().isEmpty) return;

    final trimmedNewTopic = newTopicName.trim();
    final subjectDocPath = 'app_config/courses';
    final docRef = _firestore.doc(subjectDocPath);

    await _firestore.runTransaction((transaction) async {
      final snapshot = await transaction.get(docRef);

      if (!snapshot.exists) {
        throw Exception('Courses document not found');
      }

      final data = snapshot.data()!;

      // Navigate to the specific subject
      final courses = data['courses'] as Map<String, dynamic>? ?? {};
      final course = courses[courseId] as Map<String, dynamic>? ?? {};
      final years = course['years'] as Map<String, dynamic>? ?? {};
      final year = years[yearId] as Map<String, dynamic>? ?? {};
      final subjects = year['subjects'] as Map<String, dynamic>? ?? {};
      final subject = subjects[subjectId] as Map<String, dynamic>? ?? {};

      // Get current topics list and update the topic
      final currentTopics = List<String>.from(subject['topics'] ?? []);
      final topicIndex = currentTopics.indexOf(oldTopicName);

      if (topicIndex != -1) {
        currentTopics[topicIndex] = trimmedNewTopic;
        currentTopics.sort(); // Keep topics sorted

        // Update the subject with new topics list
        final updatedSubject = {
          ...subject,
          'topics': currentTopics,
        };

        // Update the nested structure (same as add)
        final updatedSubjects = {
          ...subjects,
          subjectId: updatedSubject,
        };

        final updatedYear = {
          ...year,
          'subjects': updatedSubjects,
        };

        final updatedYears = {
          ...years,
          yearId: updatedYear,
        };

        final updatedCourse = {
          ...course,
          'years': updatedYears,
        };

        final updatedCourses = {
          ...courses,
          courseId: updatedCourse,
        };

        final updatedData = {
          ...data,
          'courses': updatedCourses,
        };

        // Write the updated document
        transaction.update(docRef, updatedData);
      }
    });
  }

  /// Check if a topic exists in a subject
  static Future<bool> topicExists({
    required String courseId,
    required String yearId,
    required String subjectId,
    required String topicName,
  }) async {
    try {
      final subjectDocPath = 'app_config/courses';
      final snapshot = await _firestore.doc(subjectDocPath).get();

      if (!snapshot.exists) return false;

      final data = snapshot.data()!;
      final courses = data['courses'] as Map<String, dynamic>? ?? {};
      final course = courses[courseId] as Map<String, dynamic>? ?? {};
      final years = course['years'] as Map<String, dynamic>? ?? {};
      final year = years[yearId] as Map<String, dynamic>? ?? {};
      final subjects = year['subjects'] as Map<String, dynamic>? ?? {};
      final subject = subjects[subjectId] as Map<String, dynamic>? ?? {};

      final topics = List<String>.from(subject['topics'] ?? []);
      return topics.any((topic) => topic.toLowerCase() == topicName.toLowerCase());
    } catch (e) {
      return false;
    }
  }
}
