
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:entities/course_entity.dart';

class CourseTree extends ConsumerStatefulWidget {
  final String courseName;
  final CourseEntity course;
  final bool isEditable;
  final Function(String action, Map<String, String> params)? onAction;

  const CourseTree({
    super.key,
    required this.courseName,
    required this.course,
    this.isEditable = false,
    this.onAction,
  });

  @override
  ConsumerState<CourseTree> createState() => _CourseTreeState();
}

class _CourseTreeState extends ConsumerState<CourseTree> {
  final Set<String> _expandedNodes = {};

  @override
  void initState() {
    super.initState();
    _expandedNodes.addAll(widget.course.years.keys);
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCourseHeader(),
            const SizedBox(height: 16),

            if (widget.course.years.isEmpty)
              _buildEmptyState('No years configured for this course')
            else
              ...widget.course.years.entries.map((yearEntry) {
                return _buildYearNode(yearEntry.key, yearEntry.value);
              }),
          ],
        ),
      ),
    );
  }

  Widget _buildCourseHeader() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primaryContainer,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.school,
            color: Theme.of(context).colorScheme.onPrimaryContainer,
            size: 24,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.courseName,
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '${widget.course.years.length} years â€¢ ${_getTotalSubjects()} subjects â€¢ ${_getTotalTests()} tests',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ),
        if (widget.isEditable) ...[
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _handleAction('add_year', {'courseName': widget.courseName}),
            tooltip: 'Add Year',
          ),
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () => _handleAction('edit_course', {'courseName': widget.courseName}),
            tooltip: 'Edit Course',
          ),
        ],
      ],
    );
  }

  Widget _buildYearNode(String yearName, yearEntity) {
    final nodeKey = 'year_$yearName';
    final isExpanded = _expandedNodes.contains(nodeKey);
    final subjectCount = yearEntity.subjects.length;

    return Column(
      children: [
        _buildTreeNode(
          level: 1,
          icon: Icons.calendar_today,
          title: yearName,
          subtitle: '$subjectCount subjects',
          isExpanded: isExpanded,
          hasChildren: subjectCount > 0,
          onToggle: () => _toggleNode(nodeKey),
          actions: widget.isEditable
              ? [
                  IconButton(
                    icon: const Icon(Icons.add, size: 18),
                    onPressed: () => _handleAction('add_subject', {
                      'courseName': widget.courseName,
                      'yearName': yearName,
                    }),
                    tooltip: 'Add Subject',
                  ),
                  IconButton(
                    icon: const Icon(Icons.edit, size: 18),
                    onPressed: () => _handleAction('edit_year', {
                      'courseName': widget.courseName,
                      'yearName': yearName,
                    }),
                    tooltip: 'Edit Year',
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete, size: 18),
                    onPressed: () => _handleAction('delete_year', {
                      'courseName': widget.courseName,
                      'yearName': yearName,
                    }),
                    tooltip: 'Delete Year',
                  ),
                ]
              : null,
        ),
        if (isExpanded && subjectCount > 0)
          ...yearEntity.subjects.entries.map((subjectEntry) {
            return _buildSubjectNode(yearName, subjectEntry.key, subjectEntry.value);
          }),
      ],
    );
  }

  Widget _buildSubjectNode(String yearName, String subjectName, subjectEntity) {
    final nodeKey = 'subject_${yearName}_$subjectName';
    final isExpanded = _expandedNodes.contains(nodeKey);
    final sectionCount = subjectEntity.sections.length;

    return Column(
      children: [
        _buildTreeNode(
          level: 2,
          icon: Icons.book,
          title: subjectName,
          subtitle: '$sectionCount sections',
          isExpanded: isExpanded,
          hasChildren: sectionCount > 0,
          onToggle: () => _toggleNode(nodeKey),
          actions: widget.isEditable
              ? [
                  IconButton(
                    icon: const Icon(Icons.add, size: 16),
                    onPressed: () => _handleAction('add_section', {
                      'courseName': widget.courseName,
                      'yearName': yearName,
                      'subjectName': subjectName,
                    }),
                    tooltip: 'Add Section',
                  ),
                  IconButton(
                    icon: const Icon(Icons.edit, size: 16),
                    onPressed: () => _handleAction('edit_subject', {
                      'courseName': widget.courseName,
                      'yearName': yearName,
                      'subjectName': subjectName,
                    }),
                    tooltip: 'Edit Subject',
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete, size: 16),
                    onPressed: () => _handleAction('delete_subject', {
                      'courseName': widget.courseName,
                      'yearName': yearName,
                      'subjectName': subjectName,
                    }),
                    tooltip: 'Delete Subject',
                  ),
                ]
              : null,
        ),
        if (isExpanded && sectionCount > 0)
          ...subjectEntity.sections.entries.map((sectionEntry) {
            return _buildSectionNode(yearName, subjectName, sectionEntry.key, sectionEntry.value);
          }),
      ],
    );
  }

  Widget _buildSectionNode(String yearName, String subjectName, String sectionName, sectionEntity) {
    final nodeKey = 'section_${yearName}_${subjectName}_$sectionName';
    final isExpanded = _expandedNodes.contains(nodeKey);
    final topicCount = sectionEntity.topics.length;

    return Column(
      children: [
        _buildTreeNode(
          level: 3,
          icon: Icons.folder,
          title: sectionName,
          subtitle: '$topicCount topics',
          isExpanded: isExpanded,
          hasChildren: topicCount > 0,
          onToggle: () => _toggleNode(nodeKey),
          actions: widget.isEditable
              ? [
                  IconButton(
                    icon: const Icon(Icons.add, size: 14),
                    onPressed: () => _handleAction('add_topic', {
                      'courseName': widget.courseName,
                      'yearName': yearName,
                      'subjectName': subjectName,
                      'sectionName': sectionName,
                    }),
                    tooltip: 'Add Topic',
                  ),
                  IconButton(
                    icon: const Icon(Icons.edit, size: 14),
                    onPressed: () => _handleAction('edit_section', {
                      'courseName': widget.courseName,
                      'yearName': yearName,
                      'subjectName': subjectName,
                      'sectionName': sectionName,
                    }),
                    tooltip: 'Edit Section',
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete, size: 14),
                    onPressed: () => _handleAction('delete_section', {
                      'courseName': widget.courseName,
                      'yearName': yearName,
                      'subjectName': subjectName,
                      'sectionName': sectionName,
                    }),
                    tooltip: 'Delete Section',
                  ),
                ]
              : null,
        ),
        if (isExpanded && topicCount > 0)
          ...sectionEntity.topics.entries.map((topicEntry) {
            return _buildTopicNode(yearName, subjectName, sectionName, topicEntry.key, topicEntry.value);
          }),
      ],
    );
  }

  Widget _buildTopicNode(String yearName, String subjectName, String sectionName, String topicName, topicEntity) {
    final testCount = topicEntity.tests.length;

    return _buildTreeNode(
      level: 4,
      icon: Icons.topic,
      title: topicName,
      subtitle: '$testCount tests',
      isExpanded: false,
      hasChildren: false,
      onToggle: null,
      actions: widget.isEditable
          ? [
              IconButton(
                icon: const Icon(Icons.quiz, size: 12),
                onPressed: () => _handleAction('manage_tests', {
                  'courseName': widget.courseName,
                  'yearName': yearName,
                  'subjectName': subjectName,
                  'sectionName': sectionName,
                  'topicName': topicName,
                }),
                tooltip: 'Manage Tests',
              ),
              IconButton(
                icon: const Icon(Icons.edit, size: 12),
                onPressed: () => _handleAction('edit_topic', {
                  'courseName': widget.courseName,
                  'yearName': yearName,
                  'subjectName': subjectName,
                  'sectionName': sectionName,
                  'topicName': topicName,
                }),
                tooltip: 'Edit Topic',
              ),
              IconButton(
                icon: const Icon(Icons.delete, size: 12),
                onPressed: () => _handleAction('delete_topic', {
                  'courseName': widget.courseName,
                  'yearName': yearName,
                  'subjectName': subjectName,
                  'sectionName': sectionName,
                  'topicName': topicName,
                }),
                tooltip: 'Delete Topic',
              ),
            ]
          : null,
    );
  }

  Widget _buildTreeNode({
    required int level,
    required IconData icon,
    required String title,
    required String subtitle,
    required bool isExpanded,
    required bool hasChildren,
    required VoidCallback? onToggle,
    List<Widget>? actions,
  }) {
    final indentation = (level - 1) * 24.0;

    return Container(
      margin: EdgeInsets.only(left: indentation, bottom: 4),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: hasChildren ? onToggle : null,
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
            child: Row(
              children: [
                SizedBox(
                  width: 24,
                  child: hasChildren
                      ? Icon(
                          isExpanded ? Icons.expand_more : Icons.chevron_right,
                          size: 20,
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                        )
                      : null,
                ),
                const SizedBox(width: 8),

                Icon(
                  icon,
                  size: 18,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 12),

                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      if (subtitle.isNotEmpty)
                        Text(
                          subtitle,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                          ),
                        ),
                    ],
                  ),
                ),

                if (actions != null) ...actions,
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(String message) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          Icon(
            Icons.folder_open,
            size: 48,
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _toggleNode(String nodeKey) {
    setState(() {
      if (_expandedNodes.contains(nodeKey)) {
        _expandedNodes.remove(nodeKey);
      } else {
        _expandedNodes.add(nodeKey);
      }
    });
  }

  void _handleAction(String action, Map<String, String> params) {
    widget.onAction?.call(action, params);
  }

  int _getTotalSubjects() {
    int total = 0;
    for (final year in widget.course.years.values) {
      total += year.subjects.length;
    }
    return total;
  }

  int _getTotalTests() {
    int total = 0;
    for (final year in widget.course.years.values) {
      for (final subject in year.subjects.values) {
        for (final section in subject.sections.values) {
          for (final topic in section.topics.values) {
            total += topic.tests.length;
          }
        }
      }
    }
    return total;
  }
}
