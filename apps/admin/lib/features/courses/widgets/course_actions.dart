
import 'package:entities/course_entity.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../providers/course_provider.dart';

class CourseActionMenu extends ConsumerWidget {
  final String courseId;
  final CourseEntity course;

  const CourseActionMenu({
    super.key,
    required this.courseId,
    required this.course,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return PopupMenuButton<String>(
      icon: const Icon(Icons.more_vert),
      onSelected: (value) {
        if (value == 'view') {
          _viewCourse(context);
        } else if (value == 'edit') {
          _editCourse(context);
        } else if (value == 'delete') {
          _deleteCourse(context, ref);
        }
      },
      itemBuilder: (context) => [
        const PopupMenuItem<String>(
          value: 'view',
          child: Row(
            children: [
              Icon(Icons.visibility, size: 16),
              SizedBox(width: 8),
              Text('View Details'),
            ],
          ),
        ),
        const PopupMenuItem<String>(
          value: 'edit',
          child: Row(
            children: [
              Icon(Icons.edit, size: 16),
              SizedBox(width: 8),
              Text('Edit'),
            ],
          ),
        ),
        const PopupMenuDivider(),
        PopupMenuItem<String>(
          value: 'delete',
          child: Row(
            children: [
              Icon(Icons.delete, size: 16, color: Colors.red[600]),
              const SizedBox(width: 8),
              Text('Delete', style: TextStyle(color: Colors.red[600])),
            ],
          ),
        ),
      ],
    );
  }

  void _viewCourse(BuildContext context) {
    context.push('/view-course/$courseId');
  }

  void _editCourse(BuildContext context) {
    context.push('/edit-course/$courseId');
  }

  void _deleteCourse(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      barrierDismissible: false, // Prevent dismissing during deletion
      builder: (dialogContext) => _DeleteCourseDialog(
        courseId: courseId,
        ref: ref,
      ),
    );
  }
}

class _DeleteCourseDialog extends ConsumerStatefulWidget {
  final String courseId;
  final WidgetRef ref;

  const _DeleteCourseDialog({
    required this.courseId,
    required this.ref,
  });

  @override
  ConsumerState<_DeleteCourseDialog> createState() => _DeleteCourseDialogState();
}

class _DeleteCourseDialogState extends ConsumerState<_DeleteCourseDialog> {
  bool _isDeleting = false;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Delete Course'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('Are you sure you want to delete this course? This action cannot be undone.'),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Text(
              'Course ID: ${widget.courseId}',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: _isDeleting ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: _isDeleting ? null : _deleteCourse,
          style: TextButton.styleFrom(foregroundColor: Colors.red),
          child: _isDeleting
            ? const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.red),
                ),
              )
            : const Text('Delete'),
        ),
      ],
    );
  }

  Future<void> _deleteCourse() async {
    setState(() {
      _isDeleting = true;
    });

    try {
      final courseManagement = widget.ref.read(courseProvider.notifier);
      await courseManagement.deleteCourse(widget.courseId);

      if (mounted) {
        Navigator.of(context).pop();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Course "${widget.courseId}" deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isDeleting = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting course: $error'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
