
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:providers/courses_provider.dart';

import '../../../shared/widgets/status_messages.dart';

class CourseOverview extends ConsumerWidget {
  final String courseId;

  const CourseOverview({required this.courseId, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final coursesAsync = ref.watch(coursesProvider);

    return coursesAsync.when(
      loading: () => const LoadingMessage(message: "Loading course details..."),
      error: (error, stack) => ErrorMessage(
        title: "Error Loading Course",
        message: "There was a problem retrieving the course details.",
        onRetry: () => ref.invalidate(coursesProvider),
      ),
      data: (coursesEntity) {
        final course = coursesEntity.courses[courseId];

        if (course == null) {
          return const ErrorMessage(
            title: "Course Not Found",
            message: "The requested course could not be found.",
          );
        }

        return SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Course: $courseId',
                        style: const TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '${course.years.length} years â€¢ ${course.years.values.map((year) => year.subjects.length).fold(0, (sum, count) => sum + count)} total subjects',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              ...course.years.entries.map((yearEntry) {
                final yearName = yearEntry.key;
                final year = yearEntry.value;
                return Card(
                  margin: const EdgeInsets.only(bottom: 16),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          yearName,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 12),
                        if (year.subjects.isEmpty)
                          Text(
                            'No subjects configured',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontStyle: FontStyle.italic,
                            ),
                          )
                        else
                          Wrap(
                            spacing: 8,
                            runSpacing: 8,
                            children: year.subjects.entries.map((subjectEntry) {
                              final subjectName = subjectEntry.key;
                              return Chip(
                                label: Text(subjectName),
                                backgroundColor: Colors.blue.shade50,
                              );
                            }).toList(),
                          ),
                      ],
                    ),
                  ),
                );
              }),
            ],
          ),
        );
      },
    );
  }
}
