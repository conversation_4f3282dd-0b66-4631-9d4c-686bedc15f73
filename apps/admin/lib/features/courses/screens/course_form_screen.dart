
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../providers/course_provider.dart';
import '../widgets/course_form.dart';
import 'package:entities/entities.dart';

class CourseFormScreen extends ConsumerStatefulWidget {
  final String? courseId;

  const CourseFormScreen({this.courseId, super.key});

  @override
  ConsumerState<CourseFormScreen> createState() => _CourseFormScreenState();
}

class _CourseFormScreenState extends ConsumerState<CourseFormScreen> {
  CourseFormData? _currentFormData;

  bool get isEditMode => widget.courseId != null;
  String get title => isEditMode ? 'Edit Course' : 'Create Course';
  String get saveButtonText => isEditMode ? 'Update Course' : 'Create Course';

  @override
  Widget build(BuildContext context) {
    final coursesAsync = ref.watch(courseProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(title),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        elevation: 0,
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
        actions: [
          TextButton(
            onPressed: () => context.pop(),
            child: const Text('Cancel'),
          ),
          const SizedBox(width: 8),
          FilledButton(
            onPressed: _handleSave,
            child: Text(saveButtonText),
          ),
          const SizedBox(width: 16),
        ],
      ),
      body: coursesAsync.when(
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 48, color: Theme.of(context).colorScheme.error),
              const SizedBox(height: 16),
              Text('Error loading course data: $error'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => ref.invalidate(courseProvider),
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
        data: (coursesEntity) {
          final existingCourse = isEditMode ? coursesEntity.courses[widget.courseId] : null;

          return _buildForm(existingCourse);
        },
      ),
    );
  }

  Widget _buildForm(CourseEntity? existingCourse) {
    return CourseForm(
      initialData: _getInitialFormData(existingCourse),
      onDataChanged: (formData) {
        setState(() {
          _currentFormData = formData;
        });
      },
    );
  }

  CourseFormData _getInitialFormData(CourseEntity? existingCourse) {
    if (isEditMode && existingCourse != null) {
      return CourseFormData.fromCourseEntity(widget.courseId!, existingCourse);
    }
    return CourseFormData();
  }

  Future<void> _handleSave() async {
    if (_currentFormData == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please fill in the course details')),
      );
      return;
    }

    final validationError = _currentFormData!.getValidationError();
    if (validationError != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(validationError)),
      );
      return;
    }

    try {
      final courseManagement = ref.read(courseProvider.notifier);

      if (isEditMode) {
        final updatedCourse = _currentFormData!.toCourseEntity();
        await courseManagement.updateCourse(_currentFormData!.courseName, updatedCourse);
      } else {
        final newCourse = _currentFormData!.toCourseEntity();
        await courseManagement.createCourse(_currentFormData!.courseName, newCourse);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Course ${isEditMode ? 'updated' : 'created'} successfully!'),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
        context.pop();
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $error'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }
}
