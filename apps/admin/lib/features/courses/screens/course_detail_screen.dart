
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../shared/widgets/app_button.dart';
import '../widgets/course_tree.dart';
import '../providers/course_provider.dart';

class CourseDetailScreen extends ConsumerWidget {
  final String courseId;

  const CourseDetailScreen({required this.courseId, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Course Details'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go('/courses'),
        ),
        actions: [
          AppButton(
            label: 'Edit',
            onPressed: () => context.push('/edit-course/$courseId'),
            variant: AppButtonVariant.outlined,
            icon: Icons.edit,
          ),
          const SizedBox(width: 8),
          OutlinedButton.icon(
            onPressed: () => _showDeleteDialog(context, ref),
            icon: const Icon(Icons.delete, color: Colors.red),
            label: const Text('Delete', style: TextStyle(color: Colors.red)),
            style: OutlinedButton.styleFrom(
              side: const BorderSide(color: Colors.red),
            ),
          ),
          const SizedBox(width: 16),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: _buildCourseView(ref),
      ),
    );
  }

  Widget _buildCourseView(WidgetRef ref) {
    final coursesAsync = ref.watch(courseProvider);

    return coursesAsync.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text('Error loading course: $error'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => ref.invalidate(courseProvider),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
      data: (coursesEntity) {
        final course = coursesEntity.courses[courseId];
        if (course == null) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.school_outlined, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text('Course not found'),
              ],
            ),
          );
        }

        return Builder(
          builder: (context) => SingleChildScrollView(
            child: CourseTree(
              courseName: courseId,
              course: course,
              isEditable: true,
              onAction: (action, params) => _handleTreeAction(context, action, params, ref),
            ),
          ),
        );
      },
    );
  }

  void _handleTreeAction(BuildContext context, String action, Map<String, String> params, WidgetRef ref) {
    final courseManagement = ref.read(courseProvider.notifier);

    switch (action) {
      case 'add_year':
        _showAddDialog(
          context,
          'Add Year',
          'Enter year name (e.g., 1st Year)',
          (value) => courseManagement.addYear(params['courseName']!, value),
        );
        break;
      case 'add_subject':
        _showAddDialog(
          context,
          'Add Subject',
          'Enter subject name',
          (value) => courseManagement.addSubject(
            params['courseName']!,
            params['yearName']!,
            value,
          ),
        );
        break;
      case 'add_section':
        _showAddDialog(
          context,
          'Add Section',
          'Enter section name',
          (value) => courseManagement.addSection(
            params['courseName']!,
            params['yearName']!,
            params['subjectName']!,
            value,
          ),
        );
        break;
      case 'add_topic':
        _showAddDialog(
          context,
          'Add Topic',
          'Enter topic name',
          (value) => courseManagement.addTopic(
            params['courseName']!,
            params['yearName']!,
            params['subjectName']!,
            params['sectionName']!,
            value,
          ),
        );
        break;
    }
  }

  void _showAddDialog(BuildContext context, String title, String hint, Future<void> Function(String) onConfirm) {
    final controller = TextEditingController();
    showDialog(
      context: context,
      builder: (dialogContext) => _AddItemDialog(
        title: title,
        hint: hint,
        controller: controller,
        onConfirm: onConfirm,
      ),
    );
  }

  void _showDeleteDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Delete Course'),
        content: const Text(
          'Are you sure you want to delete this course? This action cannot be undone and will remove all course data including years, subjects, and associated content.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(dialogContext).pop();
              try {
                final courseManagement = ref.read(courseProvider.notifier);
                await courseManagement.deleteCourse(courseId);
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Course deleted successfully')),
                  );
                  context.go('/courses');
                }
              } catch (error) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Error deleting course: $error')),
                  );
                }
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}

class _AddItemDialog extends ConsumerStatefulWidget {
  final String title;
  final String hint;
  final TextEditingController controller;
  final Future<void> Function(String) onConfirm;

  const _AddItemDialog({
    required this.title,
    required this.hint,
    required this.controller,
    required this.onConfirm,
  });

  @override
  ConsumerState<_AddItemDialog> createState() => _AddItemDialogState();
}

class _AddItemDialogState extends ConsumerState<_AddItemDialog> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.title),
      content: TextField(
        controller: widget.controller,
        decoration: InputDecoration(hintText: widget.hint),
        autofocus: true,
        enabled: !_isLoading,
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        FilledButton(
          onPressed: _isLoading ? null : _handleConfirm,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Add'),
        ),
      ],
    );
  }

  Future<void> _handleConfirm() async {
    final value = widget.controller.text.trim();
    if (value.isEmpty) return;

    setState(() => _isLoading = true);

    try {
      await widget.onConfirm(value);
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (error) {
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $error')),
        );
      }
    }
  }
}
