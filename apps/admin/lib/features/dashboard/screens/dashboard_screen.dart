// lib/features/dashboard/screens/dashboard_screen.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../shared/widgets/app_navigation_bar.dart';
import '../../../shared/widgets/user_menu_popup.dart';

class DashboardScreen extends ConsumerStatefulWidget {
  final Widget child;

  const DashboardScreen({
    super.key,
    required this.child,
  });

  @override
  ConsumerState<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends ConsumerState<DashboardScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: LayoutBuilder(
          builder: (context, constraints) {
            // If screen is too narrow, show only logo icon
            if (constraints.maxWidth < 200) {
              return Image.asset(
                'assets/logo/app_logo.png',
                height: 24,
              );
            }
            // Show both logo and text
            return Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset(
                  'assets/logo/app_logo.png',
                  height: 24,
                ),
                const SizedBox(width: 8),
                Image.asset(
                  'assets/logo/app_logo_text.png',
                  height: 16,
                ),
              ],
            );
          },
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: const UserMenuPopup(),
          ),
        ],
      ),
      body: Column(
        children: [
          const AppNavigationBar(),
          Expanded(
            child: widget.child,
          ),
        ],
      ),
    );
  }
}
