// lib/features/tests/widgets/test_status_badge.dart

import 'package:entities/test_enums.dart';
import 'package:flutter/material.dart';

class TestStatusBadge extends StatelessWidget {
  final TestStatus status;

  const TestStatusBadge({super.key, required this.status});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getStatusColor(),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(_getStatusText()),
    );
  }

  Color _getStatusColor() {
    switch (status) {
      case TestStatus.draft:
        return Colors.grey[200]!;
      case TestStatus.review:
        return Colors.orange[100]!;
      case TestStatus.published:
        return Colors.green[100]!;
    }
  }

  String _getStatusText() {
    switch (status) {
      case TestStatus.draft:
        return 'Draft';
      case TestStatus.review:
        return 'In Review';
      case TestStatus.published:
        return 'Published';
    }
  }
}
