// lib/features/tests/services/test_service.dart

import 'package:entities/question_entity.dart';
import 'package:entities/test_entity.dart';
import 'package:entities/test_enums.dart';

/// Service for handling test operations
class TestService {
  // Singleton instance
  static final TestService _instance = TestService._internal();

  // Factory constructor
  factory TestService() => _instance;

  // Internal constructor
  TestService._internal();

  /// Create a default test entity with filter values
  TestEntity createDefaultTest({
    String? courseId = '',
    String? yearId = '',
    String? subjectId = '',
    String name = 'New Test',
    QuestionType type = QuestionType.mcq,
    TestDifficulty difficulty = TestDifficulty.easy,
    TestStatus status = TestStatus.draft,
  }) {
    return TestEntity(
      courseId: courseId ?? '',
      yearId: yearId ?? '',
      subjectId: subjectId ?? '',
      name: name,
      type: type,
      difficulty: difficulty,
      status: status,
      mode: TestMode.free, // Always set to free mode for now
      questionIds: [],
      duration: 60,
      // tier field not set
    );
  }

  /// Validate a test with comprehensive checks
  String? validateTest(TestEntity test) {
    // Check hierarchy fields
    if (test.courseId.isEmpty) {
      return 'Please select a Course';
    }

    if (test.yearId.isEmpty) {
      return 'Please select a Year/Module';
    }

    if (test.subjectId.isEmpty) {
      return 'Please select a Subject';
    }

    // Check test name
    if (test.name.isEmpty) {
      return 'Test name is required';
    }

    // Check duration
    if (test.duration <= 0) {
      return 'Test duration must be greater than 0';
    }

    // Check questions - require minimum 5 questions for all tests
    if (test.questionIds.length < 5) {
      return 'Tests must have at least 5 questions. Currently selected: ${test.questionIds.length}';
    }

    // Tier validation removed temporarily
    return null; // No validation errors
  }

  /// Check if a test can be published
  String? canPublishTest(TestEntity test) {
    // First run basic validation
    final validationError = validateTest(test);
    if (validationError != null) {
      return validationError;
    }

    // Additional checks for publishing
    if (test.questionIds.length < 5) {
      return 'Tests must have at least 5 questions to be published. Current count: ${test.questionIds.length}';
    }

    return null; // No validation errors
  }

  /// Check if a test has enough questions to be published (minimum 5)
  bool hasEnoughQuestionsToPublish(TestEntity test) {
    return test.questionIds.length >= 5;
  }
}
