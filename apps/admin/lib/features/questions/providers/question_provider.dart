import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:entities/question_entity.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart' show Uint8List, debugPrint;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../services/question_service.dart';

part 'question_provider.g.dart';

/// Provider for fetching a single question by ID
@riverpod
Future<QuestionEntity?> getQuestion(Ref ref, String questionId) async {
  if (questionId.isEmpty || questionId == 'new') {
    return null;
  }

  final firestore = FirebaseFirestore.instance;

  // Use AsyncValue.guard to handle errors
  final result = await AsyncValue.guard(() async {
    final doc = await firestore.collection('questions').doc(questionId).get();

    if (!doc.exists) return null;

    final data = doc.data()!;
    data['id'] = doc.id;

    return QuestionEntity.fromJson(data);
  });

  // Log error if any
  if (result.hasError) {
    debugPrint('Error fetching question: ${result.error}');
  }

  return result.valueOrNull;
}

/// Provider for saving a question (create or update)
@riverpod
Future<bool> saveQuestion(
  Ref ref, {
  required String questionId,
  required QuestionEntity question,
  required Map<String, QuestionImageData> imageMap,
  required Map<String, String> deleteImages,
  required bool isEditMode,
}) async {
  // Use AsyncValue.guard for the entire operation
  final result = await AsyncValue.guard(() async {
    final firestore = FirebaseFirestore.instance;
    final storage = FirebaseStorage.instance;
    final questions = firestore.collection('questions');

    // Validate form
    final validationError = QuestionService().validateQuestion(question);
    if (validationError != null) {
      throw validationError;
    }

    // Process images
    final Map<String, String?> imagePaths = {};
    QuestionEntity updatedQuestion = question;

    // Handle image deletions first
    // Note: We only update the document paths, images remain in Firebase Storage
    for (final entry in deleteImages.entries) {
      final imageType = entry.key;
      // No longer delete from storage - preserve images for data recovery

      // Set the path to null/empty to remove it from the question document
      updatedQuestion = _updateQuestionForDeletedImage(updatedQuestion, imageType);
    }

    // Upload each image with bytes
    for (final entry in imageMap.entries) {
      final imageType = entry.key;
      final imageInfo = entry.value;

      // Handle new image upload
      if (imageInfo.bytes != null && imageInfo.fileName != null) {
        final path = await _uploadImage(
          storage,
          questionId,
          imageType,
          imageInfo.bytes!,
          imageInfo.fileName!,
        );
        imagePaths[imageType] = path;
        continue;
      }

      // Keep existing path
      if (imageInfo.storagePath != null) {
        imagePaths[imageType] = imageInfo.storagePath;
      }
    }

    // Update question with image paths
    updatedQuestion = updatedQuestion.copyWith(id: questionId);
    updatedQuestion = _updateQuestionWithImagePaths(updatedQuestion, imagePaths);

    // Save to Firestore
    await _saveToFirestore(questions, questionId, updatedQuestion, isEditMode);

    return true;
  });

  // Handle errors
  if (result.hasError) {
    debugPrint('Error saving question: ${result.error}');
    throw result.error!;
  }

  return result.value!;
}

/// Provider for deleting a question
/// Note: Images are preserved in Firebase Storage for data recovery purposes
@riverpod
Future<bool> deleteQuestion(Ref ref, String questionId) async {
  // Use AsyncValue.guard for the entire operation
  final result = await AsyncValue.guard(() async {
    final firestore = FirebaseFirestore.instance;
    final questions = firestore.collection('questions');

    // Only delete the document, preserve images in Firebase Storage
    // This allows for potential data recovery and prevents accidental image loss
    await AsyncValue.guard(() => questions.doc(questionId).delete());
    return true;
  });

  // Handle errors
  if (result.hasError) {
    debugPrint('Error deleting question: ${result.error}');
    throw result.error!;
  }

  return result.value!;
}

/// Provider for building a filtered query for questions
@riverpod
Query<QuestionEntity> filteredQuestionsQuery(
  Ref ref, {
  String? courseId,
  String? yearId,
  String? subjectId,
  String? topic, // Optional topic filter
  String? type,
  String? difficulty,
  String? status,
}) {
  final firestore = FirebaseFirestore.instance;
  Query<Map<String, dynamic>> query = firestore.collection('questions');

  // Apply filters if provided
  query = _applyFilters(query, {
    'courseId': courseId,
    'yearId': yearId,
    'subjectId': subjectId,
    'topic': topic, // Add topic to filters
    'type': type,
    'difficulty': difficulty,
    'status': status,
  });

  // Apply sorting
  query = query.orderBy('updatedAt', descending: true);

  // Add type conversion for QuestionEntity
  return query.withConverter<QuestionEntity>(
    fromFirestore: (snapshot, _) {
      final data = snapshot.data()!;
      data['id'] = snapshot.id;
      return QuestionEntity.fromJson(data);
    },
    toFirestore: (question, _) {
      final data = question.toJson();
      data.remove('id'); // Remove ID as it's stored in the document reference
      return data;
    },
  );
}

/// Helper function to apply filters to a query
Query<Map<String, dynamic>> _applyFilters(
  Query<Map<String, dynamic>> query,
  Map<String, String?> filters,
) {
  Query<Map<String, dynamic>> updatedQuery = query;

  filters.forEach((field, value) {
    if (value != null && value.isNotEmpty) {
      updatedQuery = updatedQuery.where(field, isEqualTo: value);
    }
  });

  return updatedQuery;
}

/// Provider for generating a new question ID
@riverpod
String generateQuestionId(Ref ref) {
  final firestore = FirebaseFirestore.instance;
  return firestore.collection('questions').doc().id;
}

/// Model for question statistics
class QuestionStats {
  final int totalQuestions;
  final int draftQuestions;
  final int reviewQuestions;
  final int publishedQuestions;
  final int mcqQuestions;
  final int flipCardQuestions;

  const QuestionStats({
    required this.totalQuestions,
    required this.draftQuestions,
    required this.reviewQuestions,
    required this.publishedQuestions,
    required this.mcqQuestions,
    required this.flipCardQuestions,
  });
}

/// Provider for getting question statistics
@riverpod
Stream<QuestionStats> questionStats(Ref ref) {
  final firestore = FirebaseFirestore.instance;

  // Listen to real-time changes in questions collection
  return firestore.collection('questions').snapshots().map((snapshot) {
    final totalQuestions = snapshot.docs.length;

    // Count by status
    int draftQuestions = 0;
    int reviewQuestions = 0;
    int publishedQuestions = 0;

    // Count by type
    int mcqQuestions = 0;
    int flipCardQuestions = 0;

    for (final doc in snapshot.docs) {
      final data = doc.data();

      // Count by status
      final status = data['status'] as String?;
      switch (status) {
        case 'draft':
          draftQuestions++;
          break;
        case 'review':
          reviewQuestions++;
          break;
        case 'published':
          publishedQuestions++;
          break;
      }

      // Count by type
      final type = data['type'] as String?;
      switch (type) {
        case 'mcq':
          mcqQuestions++;
          break;
        case 'flip_card':
          flipCardQuestions++;
          break;
      }
    }

    return QuestionStats(
      totalQuestions: totalQuestions,
      draftQuestions: draftQuestions,
      reviewQuestions: reviewQuestions,
      publishedQuestions: publishedQuestions,
      mcqQuestions: mcqQuestions,
      flipCardQuestions: flipCardQuestions,
    );
  });
}



/// Helper function to save to Firestore
Future<void> _saveToFirestore(
  CollectionReference questions,
  String questionId,
  QuestionEntity question,
  bool isEditMode
) async {
  if (isEditMode) {
    final result = await AsyncValue.guard(() =>
      questions.doc(questionId).update({
        ...question.toJson(),
        'updatedAt': FieldValue.serverTimestamp(),
      })
    );

    if (result.hasError) {
      debugPrint('Error updating question: ${result.error}');
      throw result.error!;
    }
    return;
  }

  final result = await AsyncValue.guard(() =>
    questions.doc(questionId).set({
      ...question.toJson(),
      'createdAt': FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
    })
  );

  if (result.hasError) {
    debugPrint('Error creating question: ${result.error}');
    throw result.error!;
  }
}

/// Helper function to update question with image paths
QuestionEntity _updateQuestionWithImagePaths(
  QuestionEntity question,
  Map<String, String?> imagePaths
) {
  QuestionEntity updatedQuestion = question;

  // Set question image path
  if (imagePaths.containsKey(ImageType.question)) {
    updatedQuestion = updatedQuestion.copyWith(
        questionImage: imagePaths[ImageType.question]);
  }

  // Set answer image path
  if (imagePaths.containsKey(ImageType.answer)) {
    updatedQuestion = updatedQuestion.copyWith(
        answerImage: imagePaths[ImageType.answer]);
  }

  // Set explanation image path
  if (imagePaths.containsKey(ImageType.explanation)) {
    updatedQuestion = updatedQuestion.copyWith(
        explanationImage: imagePaths[ImageType.explanation]);
  }

  // Set option image paths
  final List<String> optionImagePaths = List<String>.from(updatedQuestion.optionImages);
  for (int i = 0; i < updatedQuestion.options.length; i++) {
    final imageKey = ImageType.option(i);
    if (!imagePaths.containsKey(imageKey)) {
      continue;
    }

    // Ensure list is long enough
    while (optionImagePaths.length <= i) {
      optionImagePaths.add('');
    }

    optionImagePaths[i] = imagePaths[imageKey] ?? '';
  }

  return updatedQuestion.copyWith(optionImages: optionImagePaths);
}

/// Helper function to update question for deleted image
QuestionEntity _updateQuestionForDeletedImage(QuestionEntity question, String imageType) {
  switch (imageType) {
    case ImageType.question:
      return question.copyWith(questionImage: null);
    case ImageType.answer:
      return question.copyWith(answerImage: null);
    case ImageType.explanation:
      return question.copyWith(explanationImage: null);
    default:
      if (!imageType.startsWith(ImageType.optionPrefix)) {
        return question;
      }

      final index = int.parse(imageType.split('_')[1]);
      final optionImages = List<String>.from(question.optionImages);

      // Ensure list is long enough
      while (optionImages.length <= index) {
        optionImages.add('');
      }

      optionImages[index] = '';
      return question.copyWith(optionImages: optionImages);
  }
}

/// Helper function to upload image bytes to Firebase Storage
Future<String> _uploadImage(
  FirebaseStorage storage,
  String questionId,
  String imageType,
  Uint8List bytes,
  String fileName,
) async {
  // Generate storage path
  final extension = fileName.split('.').last.toLowerCase();
  final String storagePath = 'questions/$questionId/$imageType.$extension';
  final storageRef = storage.ref().child(storagePath);

  // Upload bytes with explicit content type using AsyncValue.guard
  final result = await AsyncValue.guard(() async {
    final uploadTask = storageRef.putData(
      bytes,
      SettableMetadata(contentType: 'image/webp'),
    );

    // Wait for upload to complete
    await uploadTask;
    return storagePath;
  });

  if (result.hasError) {
    debugPrint('Error uploading image: ${result.error}');
    throw result.error!;
  }

  return result.value!;
}
