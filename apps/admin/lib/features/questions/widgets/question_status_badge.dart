// lib/features/questions/widgets/question_status_badge.dart

import 'package:flutter/material.dart';

// Note: Questions no longer have status - this widget shows "Available" for all questions
// Status is now managed at the test level
class QuestionStatusBadge extends StatelessWidget {
  final String? status; // Kept for backward compatibility but ignored
  final double? fontSize;

  const QuestionStatusBadge({
    this.status,
    this.fontSize,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.green[100]!,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        'Available',
        style: TextStyle(
          fontSize: fontSize ?? 12,
          fontWeight: FontWeight.w500,
          color: Colors.green[800]!,
        ),
      ),
    );
  }
}