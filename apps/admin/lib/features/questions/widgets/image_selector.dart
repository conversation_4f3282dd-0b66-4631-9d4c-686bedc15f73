import 'package:file_picker/file_picker.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart' show Uint8List;
import 'package:flutter/material.dart';

class ImageSelector extends StatefulWidget {
  final Function(Uint8List?, String?) onImageSelected;
  final String? storagePath;
  final Function()? onDeleteImage;

  const ImageSelector({
    super.key,
    required this.onImageSelected,
    this.storagePath,
    this.onDeleteImage,
  });

  @override
  State<ImageSelector> createState() => _ImageSelectorState();
}

class _ImageSelectorState extends State<ImageSelector> {
  Uint8List? _imageBytes;
  String? _fileName;
  bool _isLoading = false;
  String? _imageUrl;

  @override
  void initState() {
    super.initState();
    _loadImageUrl();
  }

  @override
  void didUpdateWidget(ImageSelector oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.storagePath != oldWidget.storagePath) {
      _loadImageUrl();
    }
  }

  Future<void> _loadImageUrl() async {
    if (widget.storagePath != null && widget.storagePath!.isNotEmpty) {
      try {
        setState(() => _isLoading = true);
        final url = await FirebaseStorage.instance
            .ref(widget.storagePath)
            .getDownloadURL();
        if (mounted) {
          setState(() {
            _imageUrl = url;
            _isLoading = false;
          });
        }
      } catch (e) {
        debugPrint('Error loading image URL: $e');
        if (mounted) {
          setState(() => _isLoading = false);
        }
      }
    } else {
      setState(() => _imageUrl = null);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 100,
      height: 100,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Display either selected image, existing image, or placeholder
          if (_isLoading)
            const CircularProgressIndicator()
          else if (_imageBytes != null)
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.memory(
                _imageBytes!,
                width: 100,
                height: 100,
                fit: BoxFit.cover,
              ),
            )
          else if (_imageUrl != null)
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                _imageUrl!,
                width: 100,
                height: 100,
                fit: BoxFit.cover,
                loadingBuilder: (_, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return const Center(child: CircularProgressIndicator());
                },
                errorBuilder: (_, __, ___) => const Icon(Icons.error),
              ),
            )
          else
            Icon(Icons.add_photo_alternate, size: 100 * 0.4),

          // Upload button overlay
          Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(8),
              onTap: _selectImage,
              child: SizedBox(
                width: 100,
                height: 100,
              ),
            ),
          ),

          // Delete button if image exists
          if (_imageBytes != null || _imageUrl != null)
            Positioned(
              top: 4,
              right: 4,
              child: Container(
                decoration: const BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                ),
                child: IconButton(
                  icon: const Icon(Icons.delete, color: Colors.red, size: 20),
                  onPressed: () {
                    setState(() {
                      _imageBytes = null;
                      _fileName = null;
                      _imageUrl = null;
                    });
                    // Call the delete callback if provided
                    if (widget.onDeleteImage != null) {
                      widget.onDeleteImage!();
                    } else {
                      // Otherwise just clear the image
                      widget.onImageSelected(null, null);
                    }
                  },
                  constraints: const BoxConstraints(
                    minWidth: 24,
                    minHeight: 24,
                  ),
                  padding: EdgeInsets.zero,
                  iconSize: 20,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Future<void> _selectImage() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Show image requirements dialog
      if (context.mounted) {
        await showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Image Requirements'),
            content: const Text('Please select a WebP image under 500KB.'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }

      // Use FilePicker to select WebP image
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['webp'],
        withData: true, // Important for web to get the file bytes
      );

      if (result != null && result.files.isNotEmpty) {
        PlatformFile file = result.files.first;

        // Check file size (500KB limit)
        if (file.size > 500 * 1024) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Image is too large. Maximum size is 500KB.'),
              ),
            );
          }
          setState(() {
            _isLoading = false;
          });
          return;
        }

        // Save image data
        if (file.bytes != null) {
          setState(() {
            _imageBytes = file.bytes!;
            _fileName = file.name;
            _imageUrl = null; // Clear existing URL
            _isLoading = false;
          });

          // Pass the selected image to parent
          widget.onImageSelected(_imageBytes!, _fileName!);
        }
      } else {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error picking image: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text('Error picking image. Please try again.')),
        );
      }
      setState(() {
        _isLoading = false;
      });
    }
  }
}
