import 'package:entities/question_entity.dart';
import 'package:flutter/foundation.dart' show Uint8List;

/// Constants for image types
class ImageType {
  static const String question = 'question';
  static const String answer = 'answer';
  static const String explanation = 'explanation';
  static const String optionPrefix = 'option_';

  /// Generate option image type with index
  static String option(int index) => '$optionPrefix$index';
}

/// Simple class to hold image information
class QuestionImageData {
  final Uint8List? bytes;
  final String? fileName;
  final String? storagePath;

  QuestionImageData({this.bytes, this.fileName, this.storagePath});
}

/// Service for handling question operations
class QuestionService {
  // Singleton instance
  static final QuestionService _instance = QuestionService._internal();

  // Factory constructor
  factory QuestionService() => _instance;

  // Internal constructor
  QuestionService._internal();

  /// Convert a question from one type to another
  QuestionEntity convertQuestionType(
      QuestionEntity question, QuestionType newType) {
    if (question.type == newType) return question;

    if (question.type == QuestionType.mcq && newType == QuestionType.flipCard) {
      // Convert MCQ to FlipCard
      String? correctOptionText;
      String? correctOptionPath;

      if (question.correctOptionIndex >= 0 &&
          question.correctOptionIndex < question.options.length) {
        correctOptionText = question.options[question.correctOptionIndex];

        if (question.correctOptionIndex < question.optionImages.length) {
          final imagePath = question.optionImages[question.correctOptionIndex];
          if (imagePath.isNotEmpty) {
            correctOptionPath = imagePath;
          }
        }
      }

      return question.copyWith(
        type: newType,
        answer: correctOptionText,
        answerImage: correctOptionPath,
      );
    } else {
      // Convert FlipCard to MCQ
      final List<String> newOptions = ['', '', ''];
      final List<String> newOptionImages = ['', '', ''];

      if (question.answer != null && question.answer!.isNotEmpty) {
        newOptions[0] = question.answer!;
        if (question.answerImage != null && question.answerImage!.isNotEmpty) {
          newOptionImages[0] = question.answerImage!;
        }
      }

      return question.copyWith(
        type: newType,
        options: newOptions,
        optionImages: newOptionImages,
        correctOptionIndex: 0,
      );
    }
  }

  /// Create a default question entity with filter values
  QuestionEntity createDefaultQuestion({
    String? courseId = '',
    String? yearId = '',
    String? subjectId = '',
    QuestionType type = QuestionType.mcq,
  }) {
    return QuestionEntity(
        courseId: courseId ?? '',
        yearId: yearId ?? '',
        subjectId: subjectId ?? '',
        question: '',
        type: type,
        options: ['', '', '', ''],
        optionImages: ['', '', '', '']);
  }

  /// Validate a question with comprehensive checks
  String? validateQuestion(QuestionEntity question) {
    // Check hierarchy fields
    if (question.courseId.isEmpty) {
      return 'Please select a Course';
    }

    if (question.yearId.isEmpty) {
      return 'Please select a Year/Module';
    }

    if (question.subjectId.isEmpty) {
      return 'Please select a Subject';
    }

    // Check question content
    if (question.question.isEmpty) {
      return 'Question text is required';
    }

    // Type-specific validation
    if (question.type == QuestionType.mcq) {
      // Check for at least three non-empty options
      int nonEmptyOptionsCount =
          question.options.where((option) => option.isNotEmpty).length;
      if (nonEmptyOptionsCount < 3) {
        return 'Multiple-choice questions require at least 3 options';
      }

      // Check that correct option index is valid
      if (question.correctOptionIndex >= question.options.length) {
        return 'Please select a correct answer';
      }

      // Check that the selected option has text
      if (question.options[question.correctOptionIndex].isEmpty) {
        return 'The correct option cannot be empty';
      }
    } else if (question.type == QuestionType.flipCard) {
      // Validate FlipCard question
      if (question.answer == null || question.answer!.isEmpty) {
        return 'Answer is required for FlipCard questions';
      }
    }

    return null; // No validation errors
  }
}
