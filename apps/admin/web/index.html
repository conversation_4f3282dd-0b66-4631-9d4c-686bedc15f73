<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="Admin panel for the Medpulse medical education platform.">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="Medpulse Admin">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>Medpulse Admin</title>
  <link rel="manifest" href="manifest.json">

  <style>
    body {
      margin: 0;
      padding: 0;
      background-color: #f5f5f5;
      height: 100vh;
      width: 100vw;
      overflow: hidden;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 100%;
      width: 100%;
      position: fixed;
      top: 0;
      left: 0;
      background-color: #ffffff;
      transition: opacity 0.5s ease-out;
    }

    .logo {
      width: 80px;
      height: 80px;
      margin-bottom: 16px;
      animation: pulse 2s infinite;
      object-fit: contain;
    }

    .app-name {
      font-size: 24px;
      font-weight: 500;
      color: #454FBF;
      margin-bottom: 16px;
    }

    .loading-text {
      font-size: 16px;
      color: #666;
      margin-bottom: 24px;
    }

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid rgba(69, 79, 191, 0.2);
      border-radius: 50%;
      border-top-color: #454FBF;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    @keyframes pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.05); }
      100% { transform: scale(1); }
    }

    .flutter-loader {
      display: none;
    }
  </style>
</head>
<body>
  <div id="loading" class="loading-container">
    <div style="display: flex; flex-direction: column; align-items: center;">
      <img src="assets/logo/app_logo.png" alt="Medpulse Logo" class="logo">
      <img src="assets/logo/admin_logo.png" alt="Medpulse Admin" style="height: 24px; margin-bottom: 24px;">
    </div>
    <div class="loading-text">Loading application...</div>
    <div class="spinner"></div>
  </div>

  <script>
    // Hide loading screen once Flutter app is loaded
    window.addEventListener('flutter-first-frame', function() {
      var loadingElement = document.getElementById('loading');
      if (loadingElement) {
        loadingElement.style.opacity = '0';
        setTimeout(function() {
          loadingElement.style.display = 'none';
        }, 500);
      }
    });
  </script>

  <script src="flutter_bootstrap.js" async></script>
</body>
</html>
