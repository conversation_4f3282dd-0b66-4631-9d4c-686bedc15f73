rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    // ADMIN OVERRIDE - Must come first to ensure admin access
    // Updated to use new single role claim system
    match /{document=**} {
      allow read, write, delete: if hasAdminAccess();
    }

    match /course_catalog/{document=**} {
      allow read: if request.auth != null;
    }

		match /published_free/{document=**} {
      allow get: if request.auth != null;
    }

    match /published_test/{document=**} {
      allow get: if request.auth != null && hasValidSubscription(request.auth.uid)
    }

    // Users collection rules
    match /users/{userId} {
      // Users can read their own document
      allow get: if isUser(userId);

      // Users can create or update their own document if not modifying subscriptions,
      // only using allowed fields, and ensuring the userId field matches the document ID
      allow create, update: if isUser(userId) && hasOnlyAllowedFields();

      // Regular users cannot delete their own documents
      allow delete: if false;
    }

    match /users/{userId}/attempts/{attemptId} {
      // Users can read their own document
      allow get: if isUser(userId);

      // Users can read and write their own document
      allow create, update: if isUser(userId);
    }

    // User claims collection - critical for role management
    match /user_claims/{userId} {
      // Only super admin can read user claims (for user management)
      allow read: if canManageUsers();

      // Only super admin can create/update user claims
      allow create, update: if canManageUsers();

      // Only super admin can delete user claims
      allow delete: if canManageUsers();
    }

    // Questions collection - content management
    match /questions/{questionId} {
      // All authenticated users can read questions
      allow read: if isAuthenticated();

      // Content creators can create questions
      allow create: if canCreateContent();

      // Content creators can edit their own questions, managers can edit all
      allow update: if canEditOwnContent(resource.data.createdBy);

      // Only managers and super admin can delete questions
      allow delete: if canManageAllContent();
    }

    // Courses collection - course management
    match /courses/{courseId} {
      // All authenticated users can read courses
      allow read: if isAuthenticated();

      // Only managers and super admin can manage courses
      allow create, update, delete: if canManageCourses();
    }

    // Tests collection - test management
    match /tests/{testId} {
      // All authenticated users can read tests
      allow read: if isAuthenticated();

      // Content creators can create tests
      allow create: if canCreateContent();

      // Content creators can edit their own tests, managers can edit all
      allow update: if canEditOwnContent(resource.data.createdBy);

      // Only managers and super admin can delete tests
      allow delete: if canManageAllContent();
    }

    // Check if user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }

    // Check if user has admin access (any admin role)
    function hasAdminAccess() {
      return request.auth != null &&
             request.auth.token.role != null &&
             request.auth.token.role in ['super_admin', 'manager', 'content_creator'];
    }



    // Check if user is modifying their own document
    function isUser(userId) {
      return request.auth.uid == userId;
    }




    // Check if request only contains allowed fields
    function hasOnlyAllowedFields() {
      let allowedFields = ['displayName', 'signedUp', 'email', 'provider', 'uid', 'courseId', 'yearId', 'bookmarks', 'results'];
      // For new documents we need to check request.resource.data.keys.
      // We do not check for non-existence since this is an extra query,
      // it is good enough to check if the final result is allowed since there are no values to be merged.
      let isNewAndAllowed =  request.resource.data.keys().hasOnly(allowedFields);
      // for existing documents we need to check request.resource.data.diff(resource.data).affectedKeys()
      let existsAndAllowed = request.resource.data.diff(resource.data).affectedKeys().hasOnly(allowedFields);
      return isNewAndAllowed || existsAndAllowed;
    }

    // Check if user has subscription for this item and it's not expired
    function hasValidSubscription(userId) {
      let userDoc = get(/databases/$(database)/documents/users/$(userId));
      let subscriptions = userDoc.data.subscriptions;
      let subscriptionId = resource.data.subscriptionId;

      return subscriptions != null
             && subscriptionId in subscriptions
             && subscriptions[subscriptionId].expiryTime > request.time;
    }

    // Role-based access control functions using new single role claim system
    function isSuperAdmin() {
      return request.auth != null && request.auth.token.role == 'super_admin';
    }

    function isManager() {
      return request.auth != null && request.auth.token.role == 'manager';
    }

    function isContentCreator() {
      return request.auth != null && request.auth.token.role == 'content_creator';
    }



    // Permission check functions based on the new 3-role hierarchy
    function canManageUsers() {
      return isSuperAdmin(); // Only super admin can manage users
    }

    function canManageAllContent() {
      return isSuperAdmin() || isManager(); // Super admin and manager can manage all content
    }

    function canCreateContent() {
      return isSuperAdmin() || isManager() || isContentCreator(); // All admin roles can create content
    }

    function canEditOwnContent(creatorId) {
      return canManageAllContent() || (isContentCreator() && request.auth.uid == creatorId);
    }



    function canManageCourses() {
      return isSuperAdmin() || isManager(); // Super admin and manager can manage courses
    }
  }
}