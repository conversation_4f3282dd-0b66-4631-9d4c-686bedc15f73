{"indexes": [{"collectionGroup": "tests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "courseId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "tests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "courseId", "order": "ASCENDING"}, {"fieldPath": "yearId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "tests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "courseId", "order": "ASCENDING"}, {"fieldPath": "yearId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "tests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "courseId", "order": "ASCENDING"}, {"fieldPath": "yearId", "order": "ASCENDING"}, {"fieldPath": "subjectId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "tests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "courseId", "order": "ASCENDING"}, {"fieldPath": "subjectId", "order": "ASCENDING"}, {"fieldPath": "yearId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "tests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "courseId", "order": "ASCENDING"}, {"fieldPath": "yearId", "order": "ASCENDING"}, {"fieldPath": "subjectId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "tests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "courseId", "order": "ASCENDING"}, {"fieldPath": "subjectId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "yearId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "tests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "courseId", "order": "ASCENDING"}, {"fieldPath": "yearId", "order": "ASCENDING"}, {"fieldPath": "subjectId", "order": "ASCENDING"}, {"fieldPath": "difficulty", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "tests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "courseId", "order": "ASCENDING"}, {"fieldPath": "yearId", "order": "ASCENDING"}, {"fieldPath": "subjectId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "tests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "courseId", "order": "ASCENDING"}, {"fieldPath": "yearId", "order": "ASCENDING"}, {"fieldPath": "subjectId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "difficulty", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "tests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "courseId", "order": "ASCENDING"}, {"fieldPath": "difficulty", "order": "ASCENDING"}, {"fieldPath": "subjectId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "yearId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "tests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "courseId", "order": "ASCENDING"}, {"fieldPath": "yearId", "order": "ASCENDING"}, {"fieldPath": "subjectId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "tests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "courseId", "order": "ASCENDING"}, {"fieldPath": "yearId", "order": "ASCENDING"}, {"fieldPath": "subjectId", "order": "ASCENDING"}, {"fieldPath": "difficulty", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "tests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "courseId", "order": "ASCENDING"}, {"fieldPath": "yearId", "order": "ASCENDING"}, {"fieldPath": "subjectId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "difficulty", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "tests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "courseId", "order": "ASCENDING"}, {"fieldPath": "difficulty", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "subjectId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "yearId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "questions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "courseId", "order": "ASCENDING"}, {"fieldPath": "yearId", "order": "ASCENDING"}, {"fieldPath": "subjectId", "order": "ASCENDING"}]}, {"collectionGroup": "questions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "courseId", "order": "ASCENDING"}, {"fieldPath": "yearId", "order": "ASCENDING"}, {"fieldPath": "subjectId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}]}, {"collectionGroup": "questions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "courseId", "order": "ASCENDING"}, {"fieldPath": "yearId", "order": "ASCENDING"}, {"fieldPath": "subjectId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "questions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "courseId", "order": "ASCENDING"}, {"fieldPath": "yearId", "order": "ASCENDING"}, {"fieldPath": "subjectId", "order": "ASCENDING"}, {"fieldPath": "difficulty", "order": "ASCENDING"}]}, {"collectionGroup": "questions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "courseId", "order": "ASCENDING"}, {"fieldPath": "yearId", "order": "ASCENDING"}, {"fieldPath": "subjectId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "questions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "courseId", "order": "ASCENDING"}, {"fieldPath": "yearId", "order": "ASCENDING"}, {"fieldPath": "subjectId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "difficulty", "order": "ASCENDING"}]}, {"collectionGroup": "questions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "courseId", "order": "ASCENDING"}, {"fieldPath": "yearId", "order": "ASCENDING"}, {"fieldPath": "subjectId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "questions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "courseId", "order": "ASCENDING"}, {"fieldPath": "yearId", "order": "ASCENDING"}, {"fieldPath": "subjectId", "order": "ASCENDING"}, {"fieldPath": "difficulty", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "questions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "courseId", "order": "ASCENDING"}, {"fieldPath": "yearId", "order": "ASCENDING"}, {"fieldPath": "subjectId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "difficulty", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "signedUp", "order": "DESCENDING"}]}], "fieldOverrides": []}