{"version": 3, "file": "task.js", "sourceRoot": "", "sources": ["../../../src/bundlingCalls/task.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAuCH,kDAiDC;AAtFD,sCAAiC;AAGjC,gDAA2C;AAmB3C;;;;;;;;;;;;;;GAcG;AACH,SAAgB,mBAAmB;AACjC,8DAA8D;AAC9D,GAAQ,EACR,eAAuC;IAEvC,8DAA8D;IAC9D,IAAI,MAAW,CAAC;IAChB,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;QACjB,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAI,GAAG,KAAK,SAAS,EAAE,CAAC;QACtB,OAAO,SAAS,CAAC;IACnB,CAAC;IACD,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;QACvB,MAAM,GAAG,EAAE,CAAC;QACZ,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACpB,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QACH,OAAO,MAAM,CAAC;IAChB,CAAC;IACD,sDAAsD;IACtD,IAAI,GAAG,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;QAC3B,OAAO,GAAG,CAAC,IAAI,EAAE,CAAC;IACpB,CAAC;IACD,gDAAgD;IAChD,IAAI,GAAG,YAAY,WAAW,EAAE,CAAC;QAC/B,OAAQ,GAAmB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACvC,CAAC;IACD,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;QAC5B,MAAM,GAAG,EAAE,CAAC;QACZ,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YAC7B,IACE,eAAe;gBACf,GAAG,KAAK,eAAe,CAAC,KAAK;gBAC7B,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EACvB,CAAC;gBACD,mEAAmE;gBACnE,qDAAqD;gBACrD,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,CAC1B,eAAe,CAAC,KAAK,EACrB,eAAe,CAAC,GAAG,CACpB,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,GAAG,CAAC,GAAG,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;YACpD,CAAC;QACH,CAAC,CAAC,CAAC;QACH,OAAO,MAAM,CAAC;IAChB,CAAC;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAED,MAAa,IAAI;IAOf;;;;;;;;;;;;OAYG;IACH,YACE,OAA+B,EAC/B,eAAmB,EACnB,YAAoB,EACpB,gBAAgC;QAEhC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,QAAQ,GAAG,eAAe,CAAC;QAChC,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAC1C,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;IAClB,CAAC;IACD;;;OAGG;IACH,eAAe;QACb,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;YAC3C,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC;QACzC,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IACD;;;OAGG;IACH,kBAAkB;QAChB,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;YAC3C,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAC9B,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IACD;;;OAGG;IACH,GAAG;QACD,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5B,OAAO,EAAE,CAAC;QACZ,CAAC;QACD,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC9B,MAAM,QAAQ,GAAS,EAAE,CAAC;QAC1B,MAAM,GAAG,GAAa,EAAE,CAAC;QACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;YAC3C,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;YACzC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAG,CAAC,CAAC;QACvC,CAAC;QACD,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,QAAQ,CAAC;QACvC,4DAA4D;QAC5D,MAAM,IAAI,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAChC,OAAO,EACP,CAAC,GAAuB,EAAE,QAAoB,EAAE,EAAE;YAChD,MAAM,SAAS,GAA0B,EAAE,CAAC;YAC5C,IAAI,GAAG,EAAE,CAAC;gBACR,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE;oBACtB,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC5B,CAAC,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,IAAI,eAAe,GAA2B,IAAI,CAAC;gBACnD,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBAC3B,eAAe,GAAG;wBAChB,KAAK,EAAE,IAAI,CAAC,iBAAiB;wBAC7B,KAAK,EAAE,CAAC;qBACT,CAAC;gBACJ,CAAC;gBACD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;oBACxB,IAAI,eAAe,EAAE,CAAC;wBACpB,eAAe,CAAC,GAAG;4BACjB,eAAe,CAAC,KAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;oBAClD,CAAC;oBACD,SAAS,CAAC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC,CAAC;oBAC/D,IAAI,eAAe,EAAE,CAAC;wBACpB,eAAe,CAAC,KAAK,GAAG,eAAe,CAAC,GAAG,CAAC;oBAC9C,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;gBAC3C,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;oBAC5B,MAAM,KAAK,GAAG,IAAI,yBAAW,CAAC,WAAW,CAAC,CAAC;oBAC3C,KAAK,CAAC,IAAI,GAAG,eAAM,CAAC,SAAS,CAAC;oBAC9B,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBAChC,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5C,CAAC;YACH,CAAC;QACH,CAAC,CACF,CAAC;QACF,OAAO,GAAG,CAAC;IACb,CAAC;IACD;;;;;OAKG;IACH,MAAM,CAAC,QAAc,EAAE,KAAa,EAAE,QAAsB;QAC1D,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;YACd,QAAQ;YACR,KAAK;YACL,QAAQ;SACT,CAAC,CAAC;IACL,CAAC;IACD;;;;OAIG;IACH,MAAM,CAAC,EAAU;QACf,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,IAAI,YAAY,GAAG,IAAI,CAAC;YACxB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;gBACrB,IAAI,CAAC,CAAC,QAAQ,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;oBACzB,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC;gBACrB,CAAC;gBACD,IAAI,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;oBACjB,YAAY,GAAG,KAAK,CAAC;gBACvB,CAAC;YACH,CAAC,CAAC,CAAC;YACH,IAAI,YAAY,EAAE,CAAC;gBACjB,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;YAC9B,CAAC;YACD,OAAO,YAAY,CAAC;QACtB,CAAC;QACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;YAC3C,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;gBACrC,MAAM,KAAK,GAAG,IAAI,yBAAW,CAAC,WAAW,CAAC,CAAC;gBAC3C,KAAK,CAAC,IAAI,GAAG,eAAM,CAAC,SAAS,CAAC;gBAC9B,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBAC9B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACxB,MAAM;YACR,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC;IACjC,CAAC;CACF;AA7JD,oBA6JC"}