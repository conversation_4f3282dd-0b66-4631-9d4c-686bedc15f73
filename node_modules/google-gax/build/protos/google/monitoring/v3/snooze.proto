// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.monitoring.v3;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/monitoring/v3/common.proto";

option csharp_namespace = "Google.Cloud.Monitoring.V3";
option go_package = "cloud.google.com/go/monitoring/apiv3/v2/monitoringpb;monitoringpb";
option java_multiple_files = true;
option java_outer_classname = "SnoozeProto";
option java_package = "com.google.monitoring.v3";
option php_namespace = "Google\\Cloud\\Monitoring\\V3";
option ruby_package = "Google::Cloud::Monitoring::V3";

// A `Snooze` will prevent any alerts from being opened, and close any that
// are already open. The `Snooze` will work on alerts that match the
// criteria defined in the `Snooze`. The `Snooze` will be active from
// `interval.start_time` through `interval.end_time`.
message Snooze {
  option (google.api.resource) = {
    type: "monitoring.googleapis.com/Snooze"
    pattern: "projects/{project}/snoozes/{snooze}"
  };

  // Criteria specific to the `AlertPolicy`s that this `Snooze` applies to. The
  // `Snooze` will suppress alerts that come from one of the `AlertPolicy`s
  // whose names are supplied.
  message Criteria {
    // The specific `AlertPolicy` names for the alert that should be snoozed.
    // The format is:
    //
    //     projects/[PROJECT_ID_OR_NUMBER]/alertPolicies/[POLICY_ID]
    //
    // There is a limit of 16 policies per snooze. This limit is checked during
    // snooze creation.
    repeated string policies = 1 [(google.api.resource_reference) = {
      type: "monitoring.googleapis.com/AlertPolicy"
    }];
  }

  // Required. The name of the `Snooze`. The format is:
  //
  //     projects/[PROJECT_ID_OR_NUMBER]/snoozes/[SNOOZE_ID]
  //
  // The ID of the `Snooze` will be generated by the system.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. This defines the criteria for applying the `Snooze`. See
  // `Criteria` for more information.
  Criteria criteria = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. The `Snooze` will be active from `interval.start_time` through
  // `interval.end_time`.
  // `interval.start_time` cannot be in the past. There is a 15 second clock
  // skew to account for the time it takes for a request to reach the API from
  // the UI.
  TimeInterval interval = 4 [(google.api.field_behavior) = REQUIRED];

  // Required. A display name for the `Snooze`. This can be, at most, 512
  // unicode characters.
  string display_name = 5 [(google.api.field_behavior) = REQUIRED];
}
