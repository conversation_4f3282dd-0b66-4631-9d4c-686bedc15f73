// Protocol Buffers - Google's data interchange format
// Copyright 2023 Google Inc.  All rights reserved.
//
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file or at
// https://developers.google.com/open-source/licenses/bsd

edition = "2023";

package protobuf_editions_test;

option features.utf8_validation = NONE;
option optimize_for = LITE_RUNTIME;

message TestMessageLite {
  string string_field = 1;
  map<string, string> string_map_field = 4;
  int32 int_field = 5;
}
