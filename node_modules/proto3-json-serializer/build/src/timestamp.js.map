{"version": 3, "file": "timestamp.js", "sourceRoot": "", "sources": ["../../typescript/src/timestamp.ts"], "names": [], "mappings": ";AAAA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,iDAAiD;AACjD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;AAUjC,SAAgB,mCAAmC,CACjD,GAAiC;;IAEjC,0DAA0D;IAC1D,MAAM,eAAe,GAAG,GAAG,CAAC,OAAO,CAAC;IACpC,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;IAC5D,0DAA0D;IAC1D,IAAI,KAAK,GAAG,MAAA,GAAG,CAAC,KAAK,0CAAE,QAAQ,GAAG,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IACnD,mEAAmE;IACnE,OAAO,KAAK,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;QACzD,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KAC5B;IACD,OAAO,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,GAAG,GAAG,KAAK,CAAC,CAAC;AACpD,CAAC;AAbD,kFAaC;AAED,SAAgB,qCAAqC,CAAC,IAAY;IAChE,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC;IAC3E,IAAI,CAAC,KAAK,EAAE;QACV,MAAM,IAAI,KAAK,CACb,yDAAyD,IAAI,qCAAqC,CACnG,CAAC;KACH;IACD,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5B,MAAM,sBAAsB,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;IAC9C,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,sBAAsB,GAAG,IAAI,CAAC,CAAC;IAC1D,4GAA4G;IAC5G,wFAAwF;IACxF,wGAAwG;IACxG,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3C,IAAI,eAAe,EAAE;QACnB,KAAK,GAAG,QAAQ,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;KAC/D;IACD,MAAM,MAAM,GAAoB,EAAE,CAAC;IACnC,IAAI,OAAO,KAAK,CAAC,EAAE;QACjB,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;KAC1B;IACD,IAAI,KAAK,KAAK,CAAC,EAAE;QACf,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;KACtB;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AA1BD,sFA0BC"}