{"version": 3, "file": "any.js", "sourceRoot": "", "sources": ["../../typescript/src/any.ts"], "names": [], "mappings": ";AAAA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,iDAAiD;AACjD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;AAMjC,qDAAgD;AAChD,iDAAiE;AAGjE,uIAAuI;AACvI,MAAM,WAAW,GAAG,IAAI,GAAG,CAAC;IAC1B,qBAAqB;IACrB,0BAA0B;IAC1B,2BAA2B;IAC3B,2BAA2B;IAC3B,wBAAwB;IACxB,2BAA2B;IAC3B,uBAAuB;CACxB,CAAC,CAAC;AAOH,SAAgB,6BAA6B,CAC3C,GAA2B,EAC3B,OAA6B;IAE7B,kEAAkE;IAClE,gGAAgG;IAChG,gCAAgC;IAChC,oGAAoG;IACpG,oCAAoC;IAEpC,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;IACnD,IAAI,IAAmB,CAAC;IACxB,IAAI;QACF,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;KAC5C;IAAC,OAAO,GAAG,EAAE;QACZ,MAAM,IAAI,KAAK,CACb,mDAAmD,QAAQ,KAAK,GAAG,EAAE,CACtE,CAAC;KACH;IACD,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAC5C,MAAM,eAAe,GAAG,IAAA,2BAAY,EAAC,YAAY,EAAE,OAAO,CAAC,CAAC;IAC5D,IAAI,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;QAC7B,OAAO;YACL,OAAO,EAAE,GAAG,CAAC,QAAQ;YACrB,KAAK,EAAE,eAAe;SACvB,CAAC;KACH;IACA,eAA8B,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC,QAAQ,CAAC;IACxD,OAAO,eAA6B,CAAC;AACvC,CAAC;AA7BD,sEA6BC;AAED,SAAgB,+BAA+B,CAC7C,IAAmB,EACnB,IAAe;IAEf,gEAAgE;IAChE,IAAI,IAAI,KAAK,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QACpE,MAAM,IAAI,KAAK,CACb,kFAAkF,CACnF,CAAC;KACH;IAED,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;IAC9B,IAAI,CAAC,OAAO,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;QAC3C,MAAM,IAAI,KAAK,CACb,qGAAqG,CACtG,CAAC;KACH;IAED,MAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;IAC9C,IAAI,IAAmB,CAAC;IACxB,IAAI;QACF,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;KAClC;IAAC,OAAO,GAAG,EAAE;QACZ,MAAM,IAAI,KAAK,CACb,qDAAqD,QAAQ,KAAK,GAAG,EAAE,CACxE,CAAC;KACH;IAED,IAAI,KAAK,GAAc,IAAI,CAAC;IAC5B,IAAI,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;QAC7B,IAAI,CAAC,CAAC,OAAO,IAAI,IAAI,CAAC,EAAE;YACtB,MAAM,IAAI,KAAK,CACb,yFAAyF,QAAQ,+BAA+B,CACjI,CAAC;SACH;QACD,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;KACpB;IAED,MAAM,YAAY,GAAG,IAAA,+BAAc,EAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACjD,IAAI,YAAY,KAAK,IAAI,EAAE;QACzB,OAAO;YACL,QAAQ,EAAE,OAAO;YACjB,KAAK,EAAE,IAAI;SACZ,CAAC;KACH;IAED,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,MAAM,EAAE,CAAC;IACtD,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC;IACjE,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAEzC,OAAO;QACL,QAAQ,EAAE,OAAO;QACjB,KAAK,EAAE,MAAM;KACd,CAAC;AACJ,CAAC;AAtDD,0EAsDC"}