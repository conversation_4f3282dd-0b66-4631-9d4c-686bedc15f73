{"version": 3, "file": "NoopTracerProvider.js", "sourceRoot": "", "sources": ["../../../src/trace/NoopTracerProvider.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAK1C;;;;;GAKG;AACH,MAAM,OAAO,kBAAkB;IAC7B,SAAS,CACP,KAAc,EACd,QAAiB,EACjB,QAAwB;QAExB,OAAO,IAAI,UAAU,EAAE,CAAC;IAC1B,CAAC;CACF", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { NoopTracer } from './NoopTracer';\nimport { Tracer } from './tracer';\nimport { TracerOptions } from './tracer_options';\nimport { TracerProvider } from './tracer_provider';\n\n/**\n * An implementation of the {@link TracerProvider} which returns an impotent\n * Tracer for all calls to `getTracer`.\n *\n * All operations are no-op.\n */\nexport class NoopTracerProvider implements TracerProvider {\n  getTracer(\n    _name?: string,\n    _version?: string,\n    _options?: TracerOptions\n  ): Tracer {\n    return new NoopTracer();\n  }\n}\n"]}