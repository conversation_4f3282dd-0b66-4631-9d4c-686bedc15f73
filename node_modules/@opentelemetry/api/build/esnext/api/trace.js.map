{"version": 3, "file": "trace.js", "sourceRoot": "", "sources": ["../../../src/api/trace.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EACL,SAAS,EACT,cAAc,EACd,gBAAgB,GACjB,MAAM,0BAA0B,CAAC;AAClC,OAAO,EAAE,mBAAmB,EAAE,MAAM,8BAA8B,CAAC;AACnE,OAAO,EACL,kBAAkB,EAClB,eAAe,GAChB,MAAM,4BAA4B,CAAC;AAGpC,OAAO,EACL,UAAU,EACV,aAAa,EACb,OAAO,EACP,cAAc,EACd,OAAO,EACP,cAAc,GACf,MAAM,wBAAwB,CAAC;AAChC,OAAO,EAAE,OAAO,EAAE,MAAM,QAAQ,CAAC;AAEjC,MAAM,QAAQ,GAAG,OAAO,CAAC;AAEzB;;GAEG;AACH,MAAM,OAAO,QAAQ;IAKnB,+FAA+F;IAC/F;QAHQ,yBAAoB,GAAG,IAAI,mBAAmB,EAAE,CAAC;QAmDlD,oBAAe,GAAG,eAAe,CAAC;QAElC,uBAAkB,GAAG,kBAAkB,CAAC;QAExC,eAAU,GAAG,UAAU,CAAC;QAExB,YAAO,GAAG,OAAO,CAAC;QAElB,kBAAa,GAAG,aAAa,CAAC;QAE9B,mBAAc,GAAG,cAAc,CAAC;QAEhC,YAAO,GAAG,OAAO,CAAC;QAElB,mBAAc,GAAG,cAAc,CAAC;IA9DhB,CAAC;IAExB,kDAAkD;IAC3C,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,QAAQ,EAAE,CAAC;SACjC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED;;;;OAIG;IACI,uBAAuB,CAAC,QAAwB;QACrD,MAAM,OAAO,GAAG,cAAc,CAC5B,QAAQ,EACR,IAAI,CAAC,oBAAoB,EACzB,OAAO,CAAC,QAAQ,EAAE,CACnB,CAAC;QACF,IAAI,OAAO,EAAE;YACX,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;SACjD;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,iBAAiB;QACtB,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,oBAAoB,CAAC;IAC1D,CAAC;IAED;;OAEG;IACI,SAAS,CAAC,IAAY,EAAE,OAAgB;QAC7C,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IAED,wCAAwC;IACjC,OAAO;QACZ,gBAAgB,CAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC/C,IAAI,CAAC,oBAAoB,GAAG,IAAI,mBAAmB,EAAE,CAAC;IACxD,CAAC;CAiBF", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  getGlobal,\n  registerGlobal,\n  unregisterGlobal,\n} from '../internal/global-utils';\nimport { ProxyTracerProvider } from '../trace/ProxyTracerProvider';\nimport {\n  isSpanContextValid,\n  wrapSpanContext,\n} from '../trace/spancontext-utils';\nimport { Tracer } from '../trace/tracer';\nimport { TracerProvider } from '../trace/tracer_provider';\nimport {\n  deleteSpan,\n  getActiveSpan,\n  getSpan,\n  getSpanContext,\n  setSpan,\n  setSpanContext,\n} from '../trace/context-utils';\nimport { DiagAPI } from './diag';\n\nconst API_NAME = 'trace';\n\n/**\n * Singleton object which represents the entry point to the OpenTelemetry Tracing API\n */\nexport class TraceAPI {\n  private static _instance?: TraceAPI;\n\n  private _proxyTracerProvider = new ProxyTracerProvider();\n\n  /** Empty private constructor prevents end users from constructing a new instance of the API */\n  private constructor() {}\n\n  /** Get the singleton instance of the Trace API */\n  public static getInstance(): TraceAPI {\n    if (!this._instance) {\n      this._instance = new TraceAPI();\n    }\n\n    return this._instance;\n  }\n\n  /**\n   * Set the current global tracer.\n   *\n   * @returns true if the tracer provider was successfully registered, else false\n   */\n  public setGlobalTracerProvider(provider: TracerProvider): boolean {\n    const success = registerGlobal(\n      API_NAME,\n      this._proxyTracerProvider,\n      DiagAPI.instance()\n    );\n    if (success) {\n      this._proxyTracerProvider.setDelegate(provider);\n    }\n    return success;\n  }\n\n  /**\n   * Returns the global tracer provider.\n   */\n  public getTracerProvider(): TracerProvider {\n    return getGlobal(API_NAME) || this._proxyTracerProvider;\n  }\n\n  /**\n   * Returns a tracer from the global tracer provider.\n   */\n  public getTracer(name: string, version?: string): Tracer {\n    return this.getTracerProvider().getTracer(name, version);\n  }\n\n  /** Remove the global tracer provider */\n  public disable() {\n    unregisterGlobal(API_NAME, DiagAPI.instance());\n    this._proxyTracerProvider = new ProxyTracerProvider();\n  }\n\n  public wrapSpanContext = wrapSpanContext;\n\n  public isSpanContextValid = isSpanContextValid;\n\n  public deleteSpan = deleteSpan;\n\n  public getSpan = getSpan;\n\n  public getActiveSpan = getActiveSpan;\n\n  public getSpanContext = getSpanContext;\n\n  public setSpan = setSpan;\n\n  public setSpanContext = setSpanContext;\n}\n"]}