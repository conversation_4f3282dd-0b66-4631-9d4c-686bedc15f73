# Pure JavaScript gRPC Client

## Installation

Node 12 is recommended. The exact set of compatible Node versions can be found in the `engines` field of the `package.json` file.

```sh
npm install @grpc/grpc-js
```

## Documentation

Documentation specifically for the `@grpc/grpc-js` package is currently not available. However, [documentation is available for the `grpc` package](https://grpc.github.io/grpc/node/grpc.html), and the two packages contain mostly the same interface. There are a few notable differences, however, and these differences are noted in the "Migrating from grpc" section below.

## Features

- Clients
- Automatic reconnection
- Servers
- Streaming
- Metadata
- Partial compression support: clients can compress and decompress messages, and servers can decompress request messages
- Pick first and round robin load balancing policies
- Client Interceptors
- Connection Keepalives
- HTTP Connect support (proxies)

If you need a feature from the `grpc` package that is not provided by the `@grpc/grpc-js`, please file a feature request with that information.

This library does not directly handle `.proto` files. To use `.proto` files with this library we recommend using the `@grpc/proto-loader` package.

## Migrating from [`grpc`](https://www.npmjs.com/package/grpc)

`@grpc/grpc-js` is almost a drop-in replacement for `grpc`, but you may need to make a few code changes to use it:

- If you are currently loading `.proto` files using `grpc.load`, that function is not available in this library. You should instead load your `.proto` files using `@grpc/proto-loader` and load the resulting package definition objects into `@grpc/grpc-js` using `grpc.loadPackageDefinition`.
- If you are currently loading packages generated by `grpc-tools`, you should instead generate your files using the `generate_package_definition` option in `grpc-tools`, then load the object exported by the generated file into `@grpc/grpc-js` using `grpc.loadPackageDefinition`.
- If you have a server and you are using `Server#bind` to bind ports, you will need to use `Server#bindAsync` instead.
- If you are using any channel options supported in `grpc` but not supported in `@grpc/grpc-js`, you may need to adjust your code to handle the different behavior. Refer to [the list of supported options](#supported-channel-options) below.
- Refer to the [detailed package comparison](https://github.com/grpc/grpc-node/blob/master/PACKAGE-COMPARISON.md) for more details on the differences between `grpc` and `@grpc/grpc-js`.

## Supported Channel Options
Many channel arguments supported in `grpc` are not supported in `@grpc/grpc-js`. The channel arguments supported by `@grpc/grpc-js` are:
  - `grpc.ssl_target_name_override`
  - `grpc.primary_user_agent`
  - `grpc.secondary_user_agent`
  - `grpc.default_authority`
  - `grpc.keepalive_time_ms`
  - `grpc.keepalive_timeout_ms`
  - `grpc.keepalive_permit_without_calls`
  - `grpc.service_config`
  - `grpc.max_concurrent_streams`
  - `grpc.initial_reconnect_backoff_ms`
  - `grpc.max_reconnect_backoff_ms`
  - `grpc.use_local_subchannel_pool`
  - `grpc.max_send_message_length`
  - `grpc.max_receive_message_length`
  - `grpc.enable_http_proxy`
  - `grpc.default_compression_algorithm`
  - `grpc.enable_channelz`
  - `grpc.dns_min_time_between_resolutions_ms`
  - `grpc.enable_retries`
  - `grpc.max_connection_age_ms`
  - `grpc.max_connection_age_grace_ms`
  - `grpc.max_connection_idle_ms`
  - `grpc.per_rpc_retry_buffer_size`
  - `grpc.retry_buffer_size`
  - `grpc.service_config_disable_resolution`
  - `grpc.client_idle_timeout_ms`
  - `grpc-node.max_session_memory`
  - `grpc-node.tls_enable_trace`
  - `grpc-node.retry_max_attempts_limit`
  - `grpc-node.flow_control_window`
  - `channelOverride`
  - `channelFactoryOverride`

## Some Notes on API Guarantees

The public API of this library follows semantic versioning, with some caveats:

- Some methods are prefixed with an underscore. These methods are internal and should not be considered part of the public API.
- The class `Call` is only exposed due to limitations of TypeScript. It should not be considered part of the public API.
- In general, any API that is exposed by this library but is not exposed by the `grpc` library is likely an error and should not be considered part of the public API.
- The `grpc.experimental` namespace contains APIs that have not stabilized. Any API in that namespace may break in any minor version update.
