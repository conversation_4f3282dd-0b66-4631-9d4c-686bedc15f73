{"version": 3, "file": "colours.js", "sourceRoot": "", "sources": ["../../src/colours.ts"], "names": [], "mappings": ";AAAA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;AAUjC;;;;;;;GAOG;AACH,MAAa,OAAO;IAelB;;;OAGG;IACH,MAAM,CAAC,SAAS,CAAC,MAAuB;QACtC,OAAO,CACL,MAAM,CAAC,KAAK;YACZ,CAAC,OAAO,MAAM,CAAC,aAAa,KAAK,UAAU;gBACzC,CAAC,CAAC,MAAM,CAAC,aAAa,EAAE,GAAG,CAAC;gBAC5B,CAAC,CAAC,IAAI,CAAC,CACV,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAAO;QACZ,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACpD,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,OAAO,CAAC,KAAK,GAAG,EAAE,CAAC;YACnB,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC;YACpB,OAAO,CAAC,GAAG,GAAG,EAAE,CAAC;YACjB,OAAO,CAAC,GAAG,GAAG,EAAE,CAAC;YACjB,OAAO,CAAC,KAAK,GAAG,EAAE,CAAC;YACnB,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC;YACpB,OAAO,CAAC,IAAI,GAAG,EAAE,CAAC;YAClB,OAAO,CAAC,OAAO,GAAG,EAAE,CAAC;YACrB,OAAO,CAAC,IAAI,GAAG,EAAE,CAAC;YAClB,OAAO,CAAC,KAAK,GAAG,EAAE,CAAC;YACnB,OAAO,CAAC,IAAI,GAAG,EAAE,CAAC;QACpB,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,KAAK,GAAG,WAAW,CAAC;YAC5B,OAAO,CAAC,MAAM,GAAG,WAAW,CAAC;YAC7B,OAAO,CAAC,GAAG,GAAG,WAAW,CAAC;YAC1B,OAAO,CAAC,GAAG,GAAG,YAAY,CAAC;YAC3B,OAAO,CAAC,KAAK,GAAG,YAAY,CAAC;YAC7B,OAAO,CAAC,MAAM,GAAG,YAAY,CAAC;YAC9B,OAAO,CAAC,IAAI,GAAG,YAAY,CAAC;YAC5B,OAAO,CAAC,OAAO,GAAG,YAAY,CAAC;YAC/B,OAAO,CAAC,IAAI,GAAG,YAAY,CAAC;YAC5B,OAAO,CAAC,KAAK,GAAG,YAAY,CAAC;YAC7B,OAAO,CAAC,IAAI,GAAG,YAAY,CAAC;QAC9B,CAAC;IACH,CAAC;;AAvDH,0BAwDC;AAvDQ,eAAO,GAAG,KAAK,CAAC;AAChB,aAAK,GAAG,EAAE,CAAC;AACX,cAAM,GAAG,EAAE,CAAC;AACZ,WAAG,GAAG,EAAE,CAAC;AAET,WAAG,GAAG,EAAE,CAAC;AACT,aAAK,GAAG,EAAE,CAAC;AACX,cAAM,GAAG,EAAE,CAAC;AACZ,YAAI,GAAG,EAAE,CAAC;AACV,eAAO,GAAG,EAAE,CAAC;AACb,YAAI,GAAG,EAAE,CAAC;AACV,aAAK,GAAG,EAAE,CAAC;AACX,YAAI,GAAG,EAAE,CAAC;AA6CnB,OAAO,CAAC,OAAO,EAAE,CAAC"}