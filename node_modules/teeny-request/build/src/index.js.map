{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAIH,2CAAuC;AACvC,mCAAuD;AACvD,6BAA6B;AAC7B,qCAAkC;AAClC,uDAAkD;AAClD,8DAA8D;AAC9D,MAAM,YAAY,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;AAqD9C,MAAa,YAAa,SAAQ,KAAK;CAEtC;AAFD,oCAEC;AAOD;;;;GAIG;AACH,SAAS,qBAAqB,CAAC,OAAgB;IAC7C,MAAM,OAAO,GAAkB;QAC7B,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,KAAK;QAC/B,GAAG,CAAC,OAAO,CAAC,OAAO,IAAI,EAAC,OAAO,EAAE,OAAO,CAAC,OAAO,EAAC,CAAC;QAClD,GAAG,CAAC,OAAO,OAAO,CAAC,IAAI,KAAK,SAAS,IAAI,EAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,EAAC,CAAC;KACnE,CAAC;IAEF,IAAI,OAAO,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE;QACpC,4CAA4C;QAC5C,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC;QACxC,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,kBAAkB,CAAC;QAErD,2CAA2C;QAC3C,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;KAC7C;SAAM;QACL,IAAI,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACjC,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;SAC7B;aAAM,IAAI,OAAO,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE;YAC3C,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SAC7C;aAAM;YACL,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;SAC7B;KACF;IAED,8DAA8D;IAC9D,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,OAAc,CAAC;IAEzC,IAAI,GAAG,GAAG,CAAE,OAA0B,CAAC,GAAG;QACvC,OAA0B,CAAC,GAAG,CAAW,CAAC;IAE7C,IAAI,CAAC,GAAG,EAAE;QACR,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;KACnD;IAED,IAAI,OAAO,CAAC,cAAc,KAAK,IAAI,IAAI,OAAO,OAAO,CAAC,EAAE,KAAK,QAAQ,EAAE;QACrE,8DAA8D;QAC9D,MAAM,EAAE,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC;QAClC,MAAM,MAAM,GAAG,EAAE,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACxC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,MAAM,CAAC;KAC1B;IAED,OAAO,CAAC,KAAK,GAAG,IAAA,iBAAQ,EAAC,GAAG,EAAE,OAAO,CAAC,CAAC;IAEvC,OAAO,EAAC,GAAG,EAAE,OAAO,EAAC,CAAC;AACxB,CAAC;AAED;;;;;;GAMG;AACH,SAAS,sBAAsB,CAAC,IAAmB,EAAE,GAAe;IAClE,MAAM,OAAO,GAAG,EAAa,CAAC;IAC9B,OAAO,CAAC,KAAK,GAAI,IAAI,CAAC,KAAe,IAAI,KAAK,CAAC;IAC/C,OAAO,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE,CAAY,CAAC;IAClD,OAAO,CAAC,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC;IACvB,oDAAoD;IACpD,MAAM,UAAU,GAAG,EAAa,CAAC;IACjC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;IAE/D,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE;QACvC,UAAU,EAAE,GAAG,CAAC,MAAM;QACtB,aAAa,EAAE,GAAG,CAAC,UAAU;QAC7B,OAAO;QACP,IAAI,EAAE,GAAG,CAAC,IAAI;QACd,OAAO,EAAE,UAAU;QACnB,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,EAAC,OAAO,EAAE,UAAU,EAAC,CAAC;KACtC,CAAC,CAAC;IAEH,OAAO,QAAoB,CAAC;AAC9B,CAAC;AAED;;;;;GAKG;AACH,SAAS,qBAAqB,CAAC,QAAgB,EAAE,SAAwB;IACvE,MAAM,MAAM,GAAG,KAAK,QAAQ,IAAI,CAAC;IACjC,MAAM,MAAM,GAAgB,IAAI,oBAAW,EAAE,CAAC;IAE9C,KAAK,MAAM,IAAI,IAAI,SAAS,EAAE;QAC5B,MAAM,QAAQ,GAAG,KAAK,QAAQ,qBAC3B,IAAoC,CAAC,cAAc,CACtD,UAAU,CAAC;QACX,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACvB,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;YACjC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxB,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;SACtB;aAAM;YACL,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAC,GAAG,EAAE,KAAK,EAAC,CAAC,CAAC;YACrC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;gBACvB,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;gBACrB,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;gBACrB,MAAM,CAAC,GAAG,EAAE,CAAC;YACf,CAAC,CAAC,CAAC;SACJ;KACF;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAID,SAAS,YAAY,CACnB,OAAgB,EAChB,QAA0B;IAE1B,MAAM,EAAC,GAAG,EAAE,OAAO,EAAC,GAAG,qBAAqB,CAAC,OAAO,CAAC,CAAC;IAEtD,MAAM,SAAS,GAAG,OAAO,CAAC,SAA0B,CAAC;IACrD,IAAI,OAAO,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;QAC/C,IAAI,CAAC,QAAQ,EAAE;YACb,4DAA4D;YAC5D,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;SACnE;QACD,MAAM,QAAQ,GAAW,IAAI,CAAC,EAAE,EAAE,CAAC;QAClC,OAAO,CAAC,OAAmB,CAC1B,cAAc,CACf,GAAG,+BAA+B,QAAQ,EAAE,CAAC;QAC9C,OAAO,CAAC,IAAI,GAAG,qBAAqB,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;QAE1D,mBAAmB;QACnB,YAAY,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;QACrC,IAAA,oBAAK,EAAC,GAAG,EAAE,OAAO,CAAC,CAAC,IAAI,CACtB,GAAG,CAAC,EAAE;YACJ,YAAY,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;YACrC,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;YAC/C,MAAM,QAAQ,GAAG,sBAAsB,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;YACtD,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;YAC3B,IACE,MAAM,KAAK,kBAAkB;gBAC7B,MAAM,KAAK,iCAAiC,EAC5C;gBACA,GAAG,CAAC,IAAI,EAAE,CAAC,IAAI,CACb,IAAI,CAAC,EAAE;oBACL,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;oBACrB,QAAQ,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;gBACjC,CAAC,EACD,CAAC,GAAU,EAAE,EAAE;oBACb,QAAQ,CAAC,GAAG,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;gBAChC,CAAC,CACF,CAAC;gBACF,OAAO;aACR;YAED,GAAG,CAAC,IAAI,EAAE,CAAC,IAAI,CACb,IAAI,CAAC,EAAE;gBACL,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;gBACrB,QAAQ,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;YACjC,CAAC,EACD,GAAG,CAAC,EAAE;gBACJ,QAAQ,CAAC,GAAG,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;YAChC,CAAC,CACF,CAAC;QACJ,CAAC,EACD,GAAG,CAAC,EAAE;YACJ,YAAY,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;YACrC,QAAQ,CAAC,GAAG,EAAE,IAAK,EAAE,IAAI,CAAC,CAAC;QAC7B,CAAC,CACF,CAAC;QACF,OAAO;KACR;IAED,IAAI,QAAQ,KAAK,SAAS,EAAE;QAC1B,cAAc;QACd,MAAM,aAAa,GAAG,YAAY,CAAC,IAAI,oBAAW,EAAE,CAAC,CAAC;QACtD,8DAA8D;QAC9D,IAAI,cAAmB,CAAC;QACxB,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,EAAE;YACjC,IAAI,cAAc,EAAE;gBAClB,IAAA,iBAAQ,EAAC,cAAc,EAAE,aAAa,EAAE,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;aACnD;iBAAM;gBACL,aAAa,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG,EAAE;oBAClC,IAAA,iBAAQ,EAAC,cAAc,EAAE,aAAa,EAAE,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;gBACpD,CAAC,CAAC,CAAC;aACJ;QACH,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;QAEzB,YAAY,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;QACrC,IAAA,oBAAK,EAAC,GAAG,EAAE,OAAO,CAAC,CAAC,IAAI,CACtB,GAAG,CAAC,EAAE;YACJ,YAAY,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;YACrC,cAAc,GAAG,GAAG,CAAC,IAAI,CAAC;YAE1B,cAAc,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAU,EAAE,EAAE;gBACxC,aAAa,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,sBAAsB,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;YACtD,aAAa,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QAC3C,CAAC,EACD,GAAG,CAAC,EAAE;YACJ,YAAY,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;YACrC,aAAa,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QACnC,CAAC,CACF,CAAC;QAEF,uDAAuD;QACvD,qDAAqD;QACrD,UAAU;QACV,OAAO,aAAwB,CAAC;KACjC;IAED,4BAA4B;IAC5B,YAAY,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;IACrC,IAAA,oBAAK,EAAC,GAAG,EAAE,OAAO,CAAC,CAAC,IAAI,CACtB,GAAG,CAAC,EAAE;QACJ,YAAY,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;QACrC,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAC/C,MAAM,QAAQ,GAAG,sBAAsB,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QACtD,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;QAC3B,IACE,MAAM,KAAK,kBAAkB;YAC7B,MAAM,KAAK,iCAAiC,EAC5C;YACA,IAAI,QAAQ,CAAC,UAAU,KAAK,GAAG,EAAE;gBAC/B,oBAAoB;gBACpB,QAAQ,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;gBAC/B,OAAO;aACR;YACD,GAAG,CAAC,IAAI,EAAE,CAAC,IAAI,CACb,IAAI,CAAC,EAAE;gBACL,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;gBACrB,QAAQ,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;YACjC,CAAC,EACD,GAAG,CAAC,EAAE;gBACJ,QAAQ,CAAC,GAAG,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;YAChC,CAAC,CACF,CAAC;YACF,OAAO;SACR;QAED,GAAG,CAAC,IAAI,EAAE,CAAC,IAAI,CACb,IAAI,CAAC,EAAE;YACL,MAAM,QAAQ,GAAG,sBAAsB,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;YACtD,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;YACrB,QAAQ,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;QACjC,CAAC,EACD,GAAG,CAAC,EAAE;YACJ,QAAQ,CAAC,GAAG,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;QAChC,CAAC,CACF,CAAC;IACJ,CAAC,EACD,GAAG,CAAC,EAAE;QACJ,YAAY,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;QACrC,QAAQ,CAAC,GAAG,EAAE,IAAK,EAAE,IAAI,CAAC,CAAC;IAC7B,CAAC,CACF,CAAC;IACF,OAAO;AACT,CAAC;AAqBO,oCAAY;AAnBpB,YAAY,CAAC,QAAQ,GAAG,CAAC,QAAqB,EAAE,EAAE;IAChD,OAAO,CAAC,OAAgB,EAAE,QAA0B,EAAkB,EAAE;QACtE,MAAM,IAAI,GAAG,EAAC,GAAG,QAAQ,EAAE,GAAG,OAAO,EAAC,CAAC;QACvC,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC1B,OAAO,YAAY,CAAC,IAAI,CAAC,CAAC;SAC3B;QACD,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAC/B,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF;;GAEG;AACH,YAAY,CAAC,KAAK,GAAG,IAAI,iCAAe,EAAE,CAAC;AAE3C,YAAY,CAAC,UAAU,GAAG,GAAS,EAAE;IACnC,YAAY,CAAC,KAAK,GAAG,IAAI,iCAAe,CAAC,YAAY,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC;AAC5E,CAAC,CAAC"}